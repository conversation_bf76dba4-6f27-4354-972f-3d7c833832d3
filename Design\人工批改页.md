# 人工批改功能设计文档

## 1. 功能概述

### 1.1 背景
当前听写练习完成后直接进入AI自动评分，为了提高评分准确性和用户体验，新增人工批改功能，让用户可以根据手写内容自行判断对错。

### 1.2 目标
- 提供AI批改和人工批改两种模式选择
- 实现直观的人工批改界面
- 保持与现有流程的兼容性
- 提供准确的批改结果记录

## 2. 功能流程设计

### 2.1 整体流程图
```
听写练习完成 → 点击提交按钮 → 选择批改模式 → 执行对应批改流程 → 显示结果页
                    ↓
              ┌─────────────┐
              │  批改模式选择  │
              └─────────────┘
                    ↓
        ┌─────────────────────────┐
        │                         │
    AI批改模式                  人工批改模式
        │                         │
    ↓                         ↓
AI自动评分                  人工批改页
(原有流程)                     ↓
    ↓                     逐个批改字词
结果页面                       ↓
                          记录批改结果
                              ↓
                          结果页面
```

### 2.2 详细流程
1. **提交触发**：用户完成听写练习，点击提交按钮
2. **模式选择**：弹出批改模式选择框
3. **人工批改**：进入人工批改页面，逐个显示字词和手写内容
4. **结果记录**：记录用户的批改结果
5. **跳转结果页**：批改完成后跳转到结果页显示最终结果

## 3. 界面设计

### 3.1 批改模式选择弹窗

**位置**：听写练习页点击提交按钮后弹出

**界面元素**：
```
┌─────────────────────────────────────┐
│              选择批改模式                │
├─────────────────────────────────────┤
│                                     │
│  ┌─────────────────────────────────┐ │
│  │            AI批改                │ │
│  │      快速自动评分，结果即时显示        │ │
│  │          [选择AI批改]              │ │
│  └─────────────────────────────────┘ │
│                                     │
│  ┌─────────────────────────────────┐ │
│  │           人工批改                │ │
│  │     您来判断对错，评分更准确          │ │
│  │         [选择人工批改]              │ │
│  └─────────────────────────────────┘ │
│                                     │
│              [取消]                  │
└─────────────────────────────────────┘
```

### 3.2 人工批改页面

**页面路径**：`/pages/manual-review/manual-review`

**界面布局**：
```
┌─────────────────────────────────────────┐
│  ← 人工批改         第1题/共8题  进度[■■□□□] │
├─────────────────────────────────────────┤
│                                         │
│              目标字词                     │
│           【春天】[chūn tiān]              │
│                                         │
│           手写内容预览                     │
│    ┌─────────────────────────────────┐   │
│    │                               │   │
│    │        [手写画布图片]            │   │
│    │                               │   │
│    └─────────────────────────────────┘   │
│                                         │
│              批改选择                     │
│        ┌─────────┐  ┌─────────┐        │
│        │    对    │  │    错    │        │
│        │   ✓     │  │   ✗     │        │
│        └─────────┘  └─────────┘        │
│                                         │
│              [跳过此题]                   │
└─────────────────────────────────────────┘
```

## 4. 技术实现方案

### 4.1 文件结构
```
miniprogram/pages/manual-review/
├── manual-review.wxml    # 页面结构
├── manual-review.wxss    # 页面样式
├── manual-review.js      # 页面逻辑
└── manual-review.json    # 页面配置
```

### 4.2 数据流设计

#### 4.2.1 页面传参
```javascript
// 从练习页跳转到人工批改页
wx.navigateTo({
  url: '/pages/manual-review/manual-review',
  // 通过全局数据传递练习结果
})
```

#### 4.2.2 数据结构
```javascript
// 人工批改页面数据结构
data: {
  currentIndex: 0,              // 当前批改的题目索引
  totalCount: 0,                // 总题目数
  currentWord: {},              // 当前字词信息
  practiceWords: [],            // 所有练习字词
  canvasStates: [],             // 画布状态数组
  manualResults: [],            // 人工批改结果数组
  isCompleted: false            // 是否已完成批改
}

// 单个批改结果结构
manualResult: {
  wordIndex: 0,                 // 字词索引
  word: "春天",                 // 字词内容
  pinyin: "chūn tiān",          // 拼音
  userChoice: "correct",        // 用户选择: correct/incorrect/skip
  timestamp: "2024-01-01T00:00:00.000Z"  // 批改时间
}
```

### 4.3 核心功能实现

#### 4.3.1 批改模式选择弹窗
```javascript
// 在practice.js中修改onSubmit方法
onSubmit() {
  wx.showActionSheet({
    itemList: ['AI批改', '人工批改'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // AI批改 - 原有流程
        this.submitPractice();
      } else if (res.tapIndex === 1) {
        // 人工批改 - 新流程
        this.startManualReview();
      }
    }
  });
}
```

#### 4.3.2 人工批改页面核心方法
```javascript
// manual-review.js 核心方法

// 页面加载
onLoad(options) {
  this.loadPracticeData();
  this.initCurrentWord();
}

// 加载练习数据
loadPracticeData() {
  const challenge = app.globalData.currentChallenge;
  // 获取练习数据和画布状态
}

// 初始化当前字词
initCurrentWord() {
  const { currentIndex, practiceWords } = this.data;
  if (currentIndex < practiceWords.length) {
    this.setData({
      currentWord: practiceWords[currentIndex]
    });
  }
}

// 选择"对"
onSelectCorrect() {
  this.recordResult('correct');
  this.nextWord();
}

// 选择"错"
onSelectIncorrect() {
  this.recordResult('incorrect');
  this.nextWord();
}

// 跳过当前题
onSkip() {
  this.recordResult('skip');
  this.nextWord();
}

// 记录批改结果
recordResult(choice) {
  const { currentIndex, currentWord, manualResults } = this.data;
  
  const result = {
    wordIndex: currentIndex,
    word: currentWord.word,
    pinyin: currentWord.pinyin,
    userChoice: choice,
    timestamp: new Date().toISOString()
  };
  
  manualResults[currentIndex] = result;
  this.setData({ manualResults });
}

// 下一个字词
nextWord() {
  const { currentIndex, totalCount } = this.data;
  
  if (currentIndex + 1 < totalCount) {
    // 还有下一题
    this.setData({
      currentIndex: currentIndex + 1
    });
    this.initCurrentWord();
  } else {
    // 已完成所有批改
    this.completeBatchReview();
  }
}

// 完成批改
completeBatchReview() {
  // 整理批改结果
  const finalResults = this.generateFinalResults();
  
  // 保存到缓存供结果页使用
  wx.setStorageSync('manualReviewResults', finalResults);
  
  // 跳转到结果页
  wx.redirectTo({
    url: '/pages/result/result'
  });
}
```

## 5. 数据处理方案

### 5.1 批改结果转换
将用户的人工批改选择转换为与AI批改兼容的结果格式：

```javascript
// 转换人工批改结果为标准格式
generateFinalResults() {
  const { practiceWords, manualResults, canvasStates } = this.data;
  
  const convertedResults = manualResults.map((result, index) => {
    return {
      id: index + 1,
      pinyin: result.pinyin,
      answer: result.word,
      handwritingImageUrl: canvasStates[index]?.tempFilePath || null,
      aiText: result.word, // 人工批改时假设识别正确
      aiConfidence: 100,   // 人工批改置信度100%
      aiIsCorrect: result.userChoice === 'correct',
      manualCorrect: result.userChoice === 'correct',
      manualReview: true,  // 标记为人工批改
      reviewChoice: result.userChoice // 保留原始选择
    };
  });
  
  return {
    words: practiceWords,
    results: convertedResults,
    statistics: this.calculateStatistics(convertedResults),
    courseInfo: this.courseInfo,
    reviewType: 'manual' // 标记批改类型
  };
}
```

### 5.2 统计计算
```javascript
calculateStatistics(results) {
  const totalCount = results.length;
  const correctCount = results.filter(r => r.reviewChoice === 'correct').length;
  const incorrectCount = results.filter(r => r.reviewChoice === 'incorrect').length;
  const skipCount = results.filter(r => r.reviewChoice === 'skip').length;
  
  return {
    totalCount,
    correctCount,
    incorrectCount,
    skipCount,
    accuracy: totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0
  };
}
```

## 6. 样式设计

### 6.1 主要样式类
```css
/* manual-review.wxss */

.review-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.review-header {
  background: white;
  padding: 20rpx 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.word-display {
  text-align: center;
  padding: 40rpx 32rpx;
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
}

.target-word {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.word-pinyin {
  font-size: 28rpx;
  color: #666;
}

.handwriting-preview {
  margin: 20rpx;
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
}

.canvas-image {
  width: 100%;
  height: 300rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
}

.review-actions {
  padding: 40rpx 32rpx;
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
}

.action-buttons {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.correct-button, .incorrect-button {
  flex: 1;
  height: 120rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.correct-button {
  background: #e8f5e8;
  color: #2e7d2e;
  border: 2rpx solid #4caf50;
}

.incorrect-button {
  background: #ffeaea;
  color: #d32f2f;
  border: 2rpx solid #f44336;
}

.skip-button {
  background: #f5f5f5;
  color: #666;
  height: 80rpx;
  border-radius: 8rpx;
}
```

## 7. 兼容性考虑

### 7.1 与现有系统的兼容
- 保持现有的数据结构和接口不变
- 人工批改结果转换为与AI批改相同的格式
- 结果页面无需修改，可同时处理两种批改结果

### 7.2 数据流兼容
```javascript
// 结果页面检测批改类型
processResultData() {
  const manualResults = wx.getStorageSync('manualReviewResults');
  const aiResults = wx.getStorageSync('latestPracticeResults');
  
  if (manualResults) {
    // 处理人工批改结果
    this.processManualReviewData(manualResults);
  } else if (aiResults) {
    // 处理AI批改结果 (原有逻辑)
    this.processAIReviewData(aiResults);
  }
}
```

## 8. 开发计划

### 8.1 第一阶段：基础功能
1. 创建人工批改页面文件
2. 实现批改模式选择弹窗
3. 实现基础的字词显示和批改功能
4. 实现批改结果记录和跳转

### 8.2 第二阶段：优化完善
1. 完善页面样式和用户体验
2. 添加进度显示和返回确认
3. 优化画布图片显示效果
4. 添加批改统计和反馈

### 8.3 第三阶段：测试验证
1. 完整功能测试
2. 与现有流程的兼容性测试
3. 用户体验优化
4. 性能优化

## 9. 注意事项

### 9.1 用户体验
- 确保界面简洁直观，操作流畅
- 提供清晰的进度提示
- 支持快速批改，避免繁琐操作

### 9.2 数据安全
- 及时保存批改进度，防止意外退出丢失数据
- 确保批改结果与原练习数据的关联正确

### 9.3 性能考虑
- 优化画布图片加载和显示
- 合理管理内存使用
- 确保页面切换流畅

---

**文档版本**: v1.0  
**创建时间**: 2024年12月  
**最后更新**: 2024年12月 