# 听写练习结果页面设计文档

## 1. 页面概述

**页面名称**: 听写练习结果页面  
**文件路径**: `miniprogram/pages/result/result`  
**触发场景**: 用户查看挑战结果时弹出  
**页面类型**: 结果展示页面  

## 2. 核心功能

### 2.1 题目信息展示
- **拼音显示**: 展示标准拼音读音
- **中文答案**: 显示正确的汉字答案
- **手写内容**: 以缩略图形式展示用户在画布上的手写图像，点击可放大查看完整图像

### 2.2 识别结果分析
- **自动识别准确率**: 显示AI识别的置信度百分比
- **正确性判定**: 基于80%准确率阈值的自动判定
- **人工确认**: 提供手动标记为正确的选项

### 2.3 交互功能
- **滚动浏览**: 支持通过上下滚动查看所有题目的结果
- **状态修改**: 允许用户手动修正识别结果
- **数据保存**: 保存用户的手动确认结果

### 2.4 挑战结果统计
- **总题数**: 显示本次挑战的总题目数量
- **正确题数**: 显示判定为正确的题目数量
- **错误题数**: 显示判定为错误的题目数量
- **准确率**: 显示本次挑战的整体准确率百分比

## 3. 页面布局设计

### 3.1 顶部统计信息区域
```
┌─────────────────────────────────┐
│       听写挑战结果               │
├─────────────────────────────────┤
│                                 │
│ 总题数: 10    正确: 8           │
│                                 │
│ 错误: 2      准确率: 80%        │
│                                 │
└─────────────────────────────────┘
```

### 3.2 整体页面布局 (垂直滚动)
```
┌─────────────────────────────────┐
│  [顶部统计信息区域]             │
├─────────────────────────────────┤
│  ▼ [题目项1]                    │
│  ▼ [题目项2]                    │
│  ▼ ...                          │
│  ▼ [题目项N]                    │
└─────────────────────────────────┘
```

### 3.3 单个题目项布局
```
┌─────────────────────────────────┐
│ 📝 题目信息                     │
│   拼音: zhōng guó               │
│   答案: 中国                    │
├─────────────────────────────────┤
│ 🖼️ 用户手写 (点击放大)          │
│   ┌─────────────────────────┐   │
│   │   [手写图像缩略图]      │   │
│   └─────────────────────────┘   │
├─────────────────────────────────┤
│ 🤖 AI识别结果                   │
│   识别文字: 中国                │
│   准确率: 92%                   │
│   状态: ✅ 正确 (≥80%)          │
│   ┌─────────────────┐           │
│   │  👤 标记为正确   │           │
│   └─────────────────┘           │
└─────────────────────────────────┘
```

### 3.4 手写内容放大弹窗
当用户点击手写图像缩略图时，会弹出一个模态窗口居中显示大幅的手写图像。
```
┌─────────────────────────────────┐
│      🖼️ 用户手写详情            │
│                            ✕   │
├─────────────────────────────────┤
│ ┌─────────────────────────────┐ │
│ │                             │ │
│ │    [大幅手写图像显示区域]     │ │
│ │                             │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

## 4. 数据结构设计

### 4.1 页面数据模型
```javascript
data: {
  // 练习结果数据
  resultsList: [], // 所有题目的结果列表
  
  // 统计信息
  statistics: {
    totalCount: 0,     // 总题目数
    correctCount: 0,    // 正确题目数
    incorrectCount: 0,  // 错误题目数
    accuracy: '0%'      // 准确率
  },
  
  // 手写图像
  showLargeImageModal: false,  // 控制大图弹窗显示
  currentLargeImageUrl: '',    // 当前显示的大图URL
  currentImageIndex: -1        // 当前查看的图片索引
}
```

### 4.2 单个题目结果数据结构
```javascript
resultItem: {
  id: 1,                     // 题目ID或索引
  pinyin: 'zhōng guó',       // 拼音
  answer: '中国',            // 标准答案
  handwritingImageUrl: '...', // 手写图像URL
  aiText: '中国',            // AI识别的文字
  aiConfidence: 92,          // AI置信度百分比
  aiIsCorrect: true,         // AI判定是否正确
  manualCorrect: false       // 用户手动确认是否正确
}
```

## 5. 功能实现逻辑

### 5.1 准确率判定算法
```javascript
// 自动判定逻辑
function autoJudgeCorrectness(confidence, recognizedText, correctAnswer) {
  // 1. 置信度判定
  if (confidence >= 80) {
    return true;
  }
  
  // 2. 文字完全匹配判定
  if (recognizedText === correctAnswer) {
    return true;
  }
  
  // 3. 相似度判定 (可选)
  const similarity = calculateTextSimilarity(recognizedText, correctAnswer);
  if (similarity >= 0.8) {
    return true;
  }
  
  return false;
}
```

### 5.2 手动确认逻辑
```javascript
// 用户手动标记为正确
toggleManualCorrect(e) {
  const itemId = e.currentTarget.dataset.id;
  const resultsList = this.data.resultsList.map(item => {
    if (item.id === itemId) {
      return { ...item, manualCorrect: !item.manualCorrect };
    }
    return item;
  });
  
  this.setData({ resultsList });
  
  // 更新统计信息
  this.calculateStatistics(resultsList);
}
```

### 5.3 统计信息计算逻辑
```javascript
// 计算统计信息
calculateStatistics(resultsList) {
  const totalCount = resultsList.length;
  let correctCount = 0;
  let incorrectCount = 0;
  
  resultsList.forEach(item => {
    // 如果AI判断正确或者用户手动标记为正确，则计为正确
    if (item.aiIsCorrect || item.manualCorrect) {
      correctCount++;
    } else {
      incorrectCount++;
    }
  });
  
  // 计算准确率
  const accuracy = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) + '%' : '0%';
  
  this.setData({
    statistics: {
      totalCount,
      correctCount,
      incorrectCount,
      accuracy
    }
  });
}
```

### 5.4 图像显示逻辑
```javascript
// 从画布状态生成图像 (用于缩略图和放大图)
generateCanvasImage(canvasState) {
  return new Promise((resolve) => {
    // 注意: 此处 'previewCanvas' 可能需要动态ID或传递给函数
    // 如果在循环中为每个题目项生成，需要确保canvasId的唯一性或复用机制
    const canvas = wx.createCanvasContext('previewCanvas'); 
    
    // 重绘画布内容
    this.redrawCanvasFromState(canvas, canvasState);
    
    // 导出为图像
    wx.canvasToTempFilePath({
      canvasId: 'previewCanvas',
      success: (res) => {
        resolve(res.tempFilePath);
      }
    });
  });
}

// 显示手写内容放大弹窗
showLargeImage(e) {
  const imageUrl = e.currentTarget.dataset.imageurl;
  const index = e.currentTarget.dataset.index;
  
  if (imageUrl) {
    this.setData({
      currentLargeImageUrl: imageUrl,
      currentImageIndex: index,
      showLargeImageModal: true,
    });
  }
}
```

## 6. API接口设计

### 6.1 获取识别结果
```javascript
// 调用AI识别API
async function getRecognitionResult(canvasImageData) {
  const response = await wx.request({
    url: '/api/recognition/handwriting',
    method: 'POST',
    data: {
      image: canvasImageData,
      expectedText: correctAnswer
    }
  });
  
  return {
    recognizedText: response.data.text,
    confidence: response.data.confidence
  };
}
```

### 6.2 保存结果数据
```javascript
// 保存最终结果
async function saveResultData(resultData) {
  await wx.request({
    url: '/api/practice/save-result',
    method: 'POST',
    data: resultData
  });
}
```

## 7. 用户交互流程

### 7.1 页面打开流程
1. 用户在练习历史中点击"查看结果"或完成练习后自动跳转
2. 系统加载练习数据和所有题目的画布状态
3. （首次加载时）对每个有手写内容的题目进行AI识别，或从缓存加载识别结果
4. 计算统计信息并显示在顶部
5. 垂直滚动列表形式显示所有题目结果详情

### 7.2 结果查看与操作流程
1. 用户通过滚动页面浏览各个题目的结果。
2. 每个题目项内联展示基本信息、手写缩略图、AI识别结果和准确率。
3. 用户可点击手写缩略图，弹窗显示大幅手写内容。
4. 用户可对每个题目进行"标记为正确"的操作。
5. 用户的修改实时更新统计信息。
6. 用户可通过底部按钮返回首页。

## 8. 样式设计规范

### 8.1 色彩方案
- **正确状态**: #52c41a (绿色)
- **错误状态**: #ff4d4f (红色)  
- **待确认状态**: #faad14 (橙色)
- **背景色**: #f5f5f5 (浅灰)
- **主色调**: #1890ff (蓝色)

### 8.2 图标使用
- 📊 结果统计
- 📝 题目信息  
- ✏️ 手写内容
- 🤖 AI识别
- 👤 人工确认
- ✅ 正确标记
- ❌ 错误标记
- ⚠️ 待确认

### 8.3 布局规范
- **弹窗宽度 (通用)**: 90% 屏幕宽度，最大600rpx (适用于手写放大弹窗等)
- **主列表内容间距**: 各题目项之间 24rpx，题目项内部组件间距 16rpx
- **按钮高度**: 80rpx
- **手写缩略图区域**: 建议宽度 120rpx - 180rpx, 高度自适应或保持正方形
- **大幅手写图像区域 (弹窗内)**: 尽可能占满弹窗内容区域，例如 500rpx * 500rpx
- **字体大小**: 标题32rpx，正文28rpx，辅助信息24rpx

## 9. 性能优化考虑

### 9.1 图像加载优化
- **缩略图优先**: 优先加载和显示缩略图。
- **懒加载机制**: 只加载当前视窗内及附近题目的图像（包括缩略图）。
- **图像缓存**: 已生成的缩略图和大幅图像进行本地缓存。
- **压缩优化**: 适当压缩图像质量以提升加载速度。

### 9.2 数据处理优化
- **按需识别**: AI识别可以在题目项滚动到可视区域时触发，或在页面加载时异步批量处理。
- **异步处理**: 确保AI识别、图像生成等耗时操作不阻塞UI。
- **结果缓存**: 缓存识别结果，避免重复调用API。

## 10. 错误处理机制

### 10.1 识别失败处理
- 网络错误：显示重试按钮
- 识别超时：提供手动跳过选项
- 服务异常：显示友好错误提示

### 10.2 数据异常处理
- 画布数据损坏：显示"内容无法显示"
- 题目信息缺失：使用默认占位内容
- 结果保存失败：本地缓存并提供重试

## 11. 测试用例设计

### 11.1 功能测试
- [ ] 正常显示题目信息
- [ ] 正确渲染手写图像
- [ ] AI识别结果准确显示
- [ ] 手动确认功能正常
- [ ] 统计信息正确计算和显示

### 11.2 边界测试  
- [ ] 无手写内容的题目处理
- [ ] 识别准确率边界值测试(79%, 80%, 81%)
- [ ] 网络异常情况处理
- [ ] 大量题目的性能表现

### 11.3 用户体验测试
- [ ] 页面加载速度测试
- [ ] 交互响应性测试  
- [ ] 不同屏幕尺寸适配测试
- [ ] 长时间使用稳定性测试

---

**文档版本**: v1.1  
**创建日期**: 2024-01-20  
**更新日期**: 2024-04-20  
**负责人**: 开发团队 