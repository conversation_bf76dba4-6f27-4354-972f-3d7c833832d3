# 字库内容选择功能设计

## 1. 功能描述

字库内容选择是小学字词听写软件的核心基础功能，为学生提供丰富的学习内容来源。功能包括教材同步选择和自定义词库管理两大模块。

### 1.1 核心功能
- **教材同步**：支持人教版、部编版、北师大版教材，覆盖1-6年级
- **自定义词库**：用户可创建、编辑、分享个性化词库
- **内容分类**：写字表、识字表、词汇表三种类型
- **智能推荐**：基于学习进度推荐适合的内容

### 1.2 用户价值
- 与学校教材完全同步，提高学习针对性
- 满足个性化学习需求
- 支持家长和老师创建专属练习内容

## 2. 实现流程

### 2.1 教材选择流程
```
用户进入字库选择 → 选择教材版本 → 选择年级 → 选择学期 → 选择单元/课程 → 选择内容类型 → 确认选择 → 返回练习准备页面
```

### 2.2 自定义词库流程
```
创建词库流程：
进入自定义词库 → 创建新词库 → 设置词库信息 → 添加字词内容 → 设置拼音和释义 → 保存词库

管理词库流程：
我的词库列表 → 选择词库 → 编辑/删除/分享操作 → 确认操作
```

### 2.3 内容同步流程
```
系统启动 → 检查内容更新 → 下载新版本教材内容 → 更新本地数据库 → 提示用户更新完成
```

## 3. 业务规则

### 3.1 教材内容规则
- **版本支持**：人教版、部编版、北师大版
- **年级范围**：1-6年级，按学期划分
- **内容分类**：
  - 写字表：需要掌握书写的汉字
  - 识字表：需要认识但不要求书写的汉字
  - 词汇表：重点词汇和短语
- **内容结构**：按教材→年级→学期→单元→课程层级组织

### 3.2 自定义词库规则
- **数量限制**：每个用户最多创建20个自定义词库
- **内容限制**：每个词库最多包含500个字词
- **命名规则**：词库名称2-20个字符，不能重复
- **分享规则**：可设置为私有或公开分享
- **内容校验**：汉字必须在Unicode汉字范围内

### 3.3 学习进度关联规则
- 自动记录用户选择的教材和年级
- 根据学习完成度推荐下一阶段内容
- 支持跨年级、跨版本的内容学习

## 4. 界面设计要求

### 4.1 教材选择页面
```
┌─────────────────────────────────────────┐
│ ←  字库内容选择               🔍 搜索     │
├─────────────────────────────────────────┤
│                                        │
│  📚 选择教材版本                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │  人教版  │ │  部编版  │ │ 北师大版 │   │
│  │   📖    │ │   📖    │ │   📖    │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  🎯 选择年级                            │
│  ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐ ┌───┐ │
│  │1年│ │2年│ │3年│ │4年│ │5年│ │6年│ │
│  │级 │ │级 │ │级 │ │级 │ │级 │ │级 │ │
│  └───┘ └───┘ └───┘ └───┘ └───┘ └───┘ │
│                                        │
│  📅 选择学期                            │
│  ┌─────────────┐ ┌─────────────┐       │
│  │   上学期     │ │   下学期     │       │
│  │   🌸        │ │   🍂        │       │
│  └─────────────┘ └─────────────┘       │
│                                        │
│                              ┌───────┐ │
│                              │ 下一步 │ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.2 内容类型选择页面
```
┌─────────────────────────────────────────┐
│ ←  人教版 - 三年级上册                   │
├─────────────────────────────────────────┤
│                                        │
│  📝 选择内容类型                        │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ✏️  写字表                          │ │
│  │     需要学会书写的汉字 (120个)        │ │
│  │                              >    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 👁️  识字表                          │ │
│  │     需要认识的汉字 (200个)           │ │
│  │                              >    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 💬  词汇表                          │ │
│  │     重点词汇和短语 (150个)           │ │
│  │                              >    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│                                        │
│                              ┌───────┐ │
│                              │ 完成   │ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.3 单元课程选择页面
```
┌─────────────────────────────────────────┐
│ ←  写字表 - 三年级上册                   │
├─────────────────────────────────────────┤
│                                        │
│  📖 第一单元                            │
│  ┌─────────────────────────────────────┐ │
│  │ 第1课 大青树下的小学 ✅ 已完成       │ │
│  │ 包含汉字: 早、晨、粗、影 (8个)        │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │ 第2课 花的学校 🔄 学习中            │ │
│  │ 包含汉字: 落、荒、桃、唱 (6个)        │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │ 第3课 不懂就要问 ⭕ 未开始           │ │
│  │ 包含汉字: 背、段、练、糊 (7个)        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📖 第二单元                            │
│  ┌─────────────────────────────────────┐ │
│  │ 第4课 古诗三首 ⭕ 未开始             │ │
│  │ 包含汉字: 寒、径、斜、霜 (8个)        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│                              ┌───────┐ │
│                              │ 开始练习│ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.4 自定义词库管理页面
```
┌─────────────────────────────────────────┐
│ ←  我的词库                    ✚ 新建   │
├─────────────────────────────────────────┤
│                                        │
│  🔍 搜索词库                            │
│  ┌─────────────────────────────────────┐ │
│  │          搜索词库名称...            │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📚 我的词库 (5/20)                     │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 📝 我的专属词库                      │ │
│  │     50个字词 · 私有 · 3天前创建      │ │
│  │     ┌───┐ ┌───┐ ┌───┐ ┌───┐       │ │
│  │     │编辑│ │分享│ │复制│ │删除│       │ │
│  │     └───┘ └───┘ └───┘ └───┘       │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 🌟 难字集合                          │ │
│  │     25个字词 · 公开 · 1周前创建      │ │
│  │     ❤️ 12个赞 · 👁️ 58次浏览         │ │
│  │     ┌───┐ ┌───┐ ┌───┐ ┌───┐       │ │
│  │     │编辑│ │分享│ │复制│ │删除│       │ │
│  │     └───┘ └───┘ └───┘ └───┘       │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🎯 推荐词库                            │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 🔥 三年级易错字                      │ │
│  │     by 王老师 · 180个字词            │ │
│  │     ⭐ 4.8分 · 💾 使用               │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 4.5 创建词库页面
```
┌─────────────────────────────────────────┐
│ ←  创建新词库                  ✅ 保存   │
├─────────────────────────────────────────┤
│                                        │
│  📝 词库信息                            │
│  ┌─────────────────────────────────────┐ │
│  │ 词库名称                            │ │
│  │ 我的专属练习册                       │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │ 词库描述                            │ │
│  │ 收集日常易错的汉字，加强练习...       │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🔒 隐私设置                            │
│  ○ 私有（只有我可以使用）                │
│  ● 公开（所有人都可以使用）              │
│                                        │
│  ✏️ 添加内容 (12/500)                   │
│  ┌─────────────────────────────────────┐ │
│  │ ┌───┐ 输入汉字或词语                 │ │
│  │ │ + │ ┌─────────────────────────┐   │ │
│  │ └───┘ │        请输入...         │   │ │
│  │       └─────────────────────────┘   │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📝 已添加内容                          │
│  ┌──┬────┬─────────┬────────────────┐ │
│  │  │ 字词│   拼音   │      释义       │ │
│  ├──┼────┼─────────┼────────────────┤ │
│  │🗑│ 苹果│píng guǒ │一种水果         │ │
│  │🗑│ 困难│kùn nan  │不容易处理的事情 │ │
│  │🗑│ 温暖│wēn nuǎn │温度适宜        │ │
│  └──┴────┴─────────┴────────────────┘ │
│                                        │
│                              ┌───────┐ │
│                              │ 批量导入│ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.6 设计风格要求

1. **色彩运用**
   - 主色调：清新蓝色(#4A90E2)
   - 辅助色：温暖橙色(#FF9500)用于重点信息
   - 成功色：绿色(#52C41A)表示完成状态
   - 警告色：橙色(#FA8C16)表示进行中状态

2. **图标设计**
   - 使用符合小学生认知的直观图标
   - 教材版本使用书本图标
   - 年级使用数字+文字组合
   - 内容类型使用功能性图标

3. **交互反馈**
   - 选择状态有明显的视觉反馈
   - 加载过程显示进度提示
   - 操作结果有成功/失败提示

4. **布局特点**
   - 卡片式布局，信息层次清晰
   - 按钮尺寸适合儿童手指操作
   - 重要信息使用对比色突出显示

## 5. 技术实现

### 5.1 前端实现（微信小程序）

#### 教材数据管理
```javascript
// pages/textbook-selection/index.js
Page({
  data: {
    publishers: [
      { id: 'renjiao', name: '人教版', icon: 'book' },
      { id: 'bubian', name: '部编版', icon: 'book' },
      { id: 'beishida', name: '北师大版', icon: 'book' }
    ],
    grades: [
      { id: 1, name: '一年级' },
      { id: 2, name: '二年级' },
      { id: 3, name: '三年级' },
      { id: 4, name: '四年级' },
      { id: 5, name: '五年级' },
      { id: 6, name: '六年级' }
    ],
    semesters: [
      { id: 'first', name: '上学期' },
      { id: 'second', name: '下学期' }
    ],
    selectedPublisher: '',
    selectedGrade: '',
    selectedSemester: '',
    contentTypes: []
  },

  onLoad() {
    this.loadUserPreference();
  },

  // 加载用户偏好设置
  loadUserPreference() {
    const userConfig = wx.getStorageSync('user_textbook_config');
    if (userConfig) {
      this.setData({
        selectedPublisher: userConfig.publisher,
        selectedGrade: userConfig.grade,
        selectedSemester: userConfig.semester
      });
    }
  },

  // 选择出版社
  selectPublisher(e) {
    const publisherId = e.currentTarget.dataset.id;
    this.setData({ selectedPublisher: publisherId });
    this.loadContentTypes();
  },

  // 选择年级
  selectGrade(e) {
    const gradeId = e.currentTarget.dataset.id;
    this.setData({ selectedGrade: gradeId });
    this.loadContentTypes();
  },

  // 选择学期
  selectSemester(e) {
    const semesterId = e.currentTarget.dataset.id;
    this.setData({ selectedSemester: semesterId });
    this.loadContentTypes();
  },

  // 加载内容类型
  async loadContentTypes() {
    const { selectedPublisher, selectedGrade, selectedSemester } = this.data;
    
    if (!selectedPublisher || !selectedGrade || !selectedSemester) {
      return;
    }

    try {
      wx.showLoading({ title: '加载中...' });
      
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/textbook/content-types`,
        method: 'GET',
        data: {
          publisher: selectedPublisher,
          grade: selectedGrade,
          semester: selectedSemester
        }
      });

      this.setData({ contentTypes: res.data });
      
      // 保存用户选择
      wx.setStorageSync('user_textbook_config', {
        publisher: selectedPublisher,
        grade: selectedGrade,
        semester: selectedSemester
      });
      
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 进入下一步
  nextStep() {
    const { selectedPublisher, selectedGrade, selectedSemester } = this.data;
    
    if (!selectedPublisher || !selectedGrade || !selectedSemester) {
      wx.showToast({ title: '请完成选择', icon: 'error' });
      return;
    }

    wx.navigateTo({
      url: `/pages/content-selection/index?publisher=${selectedPublisher}&grade=${selectedGrade}&semester=${selectedSemester}`
    });
  }
});
```

#### 自定义词库管理
```javascript
// pages/custom-wordbook/index.js
Page({
  data: {
    searchKeyword: '',
    myWordbooks: [],
    recommendedWordbooks: [],
    loading: false
  },

  onLoad() {
    this.loadMyWordbooks();
    this.loadRecommendedWordbooks();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadMyWordbooks();
  },

  // 搜索词库
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
    this.searchWordbooks(e.detail.value);
  },

  // 搜索词库
  searchWordbooks: _.debounce(function(keyword) {
    if (!keyword.trim()) {
      this.loadMyWordbooks();
      return;
    }

    // 实现搜索逻辑
    const filtered = this.data.myWordbooks.filter(book => 
      book.name.includes(keyword) || book.description.includes(keyword)
    );
    this.setData({ myWordbooks: filtered });
  }, 300),

  // 加载我的词库
  async loadMyWordbooks() {
    try {
      this.setData({ loading: true });
      
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/wordbook/my-list`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      this.setData({ myWordbooks: res.data });
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载推荐词库
  async loadRecommendedWordbooks() {
    try {
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/wordbook/recommended`,
        method: 'GET'
      });

      this.setData({ recommendedWordbooks: res.data });
    } catch (error) {
      console.error('加载推荐词库失败:', error);
    }
  },

  // 创建新词库
  createWordbook() {
    wx.navigateTo({
      url: '/pages/wordbook-editor/index'
    });
  },

  // 编辑词库
  editWordbook(e) {
    const wordbookId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/wordbook-editor/index?id=${wordbookId}`
    });
  },

  // 删除词库
  async deleteWordbook(e) {
    const wordbookId = e.currentTarget.dataset.id;
    
    const result = await wx.showModal({
      title: '确认删除',
      content: '删除后不可恢复，确定要删除这个词库吗？'
    });

    if (!result.confirm) return;

    try {
      wx.showLoading({ title: '删除中...' });
      
      await wx.request({
        url: `${app.globalData.apiUrl}/wordbook/${wordbookId}`,
        method: 'DELETE',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      wx.showToast({ title: '删除成功', icon: 'success' });
      this.loadMyWordbooks(); // 刷新列表
      
    } catch (error) {
      wx.showToast({ title: '删除失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 分享词库
  shareWordbook(e) {
    const wordbook = e.currentTarget.dataset.wordbook;
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 复制词库
  async copyWordbook(e) {
    const wordbookId = e.currentTarget.dataset.id;
    
    try {
      wx.showLoading({ title: '复制中...' });
      
      await wx.request({
        url: `${app.globalData.apiUrl}/wordbook/${wordbookId}/copy`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      wx.showToast({ title: '复制成功', icon: 'success' });
      this.loadMyWordbooks(); // 刷新列表
      
    } catch (error) {
      wx.showToast({ title: '复制失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 使用推荐词库
  async useRecommendedWordbook(e) {
    const wordbookId = e.currentTarget.dataset.id;
    
    try {
      wx.showLoading({ title: '添加中...' });
      
      await wx.request({
        url: `${app.globalData.apiUrl}/wordbook/${wordbookId}/use`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      wx.showToast({ title: '添加成功', icon: 'success' });
      this.loadMyWordbooks(); // 刷新我的词库列表
      
    } catch (error) {
      wx.showToast({ title: '添加失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  }
});
```

### 5.2 后端实现（Node.js）

#### 教材内容API
```javascript
// routes/textbook.js
const express = require('express');
const router = express.Router();
const TextbookService = require('../services/textbook');

// 获取内容类型
router.get('/content-types', async (req, res) => {
  try {
    const { publisher, grade, semester } = req.query;
    
    const contentTypes = await TextbookService.getContentTypes({
      publisher,
      grade: parseInt(grade),
      semester
    });
    
    res.json(contentTypes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取单元课程列表
router.get('/units', async (req, res) => {
  try {
    const { publisher, grade, semester, type } = req.query;
    
    const units = await TextbookService.getUnits({
      publisher,
      grade: parseInt(grade),
      semester,
      type
    });
    
    res.json(units);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取课程内容
router.get('/lessons/:lessonId/content', async (req, res) => {
  try {
    const { lessonId } = req.params;
    const { type } = req.query;
    
    const content = await TextbookService.getLessonContent(lessonId, type);
    
    res.json(content);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

#### 自定义词库API
```javascript
// routes/wordbook.js
const express = require('express');
const router = express.Router();
const WordbookService = require('../services/wordbook');
const auth = require('../middleware/auth');

// 获取我的词库列表
router.get('/my-list', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const wordbooks = await WordbookService.getUserWordbooks(userId);
    
    res.json(wordbooks);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 创建词库
router.post('/create', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, description, isPublic, words } = req.body;
    
    // 验证用户词库数量限制
    const userWordbookCount = await WordbookService.getUserWordbookCount(userId);
    if (userWordbookCount >= 20) {
      return res.status(400).json({ error: '词库数量已达上限' });
    }
    
    // 验证词库内容数量
    if (words.length > 500) {
      return res.status(400).json({ error: '词库内容数量超出限制' });
    }
    
    const wordbook = await WordbookService.createWordbook({
      userId,
      name,
      description,
      isPublic,
      words
    });
    
    res.json(wordbook);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新词库
router.put('/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const updateData = req.body;
    
    // 验证所有权
    const wordbook = await WordbookService.getWordbookById(id);
    if (!wordbook || wordbook.user_id !== userId) {
      return res.status(403).json({ error: '无权限操作' });
    }
    
    const updatedWordbook = await WordbookService.updateWordbook(id, updateData);
    
    res.json(updatedWordbook);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 删除词库
router.delete('/:id', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    
    // 验证所有权
    const wordbook = await WordbookService.getWordbookById(id);
    if (!wordbook || wordbook.user_id !== userId) {
      return res.status(403).json({ error: '无权限操作' });
    }
    
    await WordbookService.deleteWordbook(id);
    
    res.json({ message: '删除成功' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 复制词库
router.post('/:id/copy', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    
    // 验证用户词库数量限制
    const userWordbookCount = await WordbookService.getUserWordbookCount(userId);
    if (userWordbookCount >= 20) {
      return res.status(400).json({ error: '词库数量已达上限' });
    }
    
    const copiedWordbook = await WordbookService.copyWordbook(id, userId);
    
    res.json(copiedWordbook);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取推荐词库
router.get('/recommended', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    
    const recommended = await WordbookService.getRecommendedWordbooks({
      page: parseInt(page),
      limit: parseInt(limit)
    });
    
    res.json(recommended);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

### 5.3 数据服务层
```javascript
// services/textbook.js
const db = require('../config/database');

class TextbookService {
  // 获取内容类型统计
  static async getContentTypes({ publisher, grade, semester }) {
    const query = `
      SELECT 
        content_type,
        COUNT(*) as word_count
      FROM textbook_words tw
      JOIN textbook_lessons tl ON tw.lesson_id = tl.id
      JOIN textbook_units tu ON tl.unit_id = tu.id
      WHERE tu.publisher = ? AND tu.grade = ? AND tu.semester = ?
      GROUP BY content_type
    `;
    
    const [rows] = await db.execute(query, [publisher, grade, semester]);
    
    return rows.map(row => ({
      type: row.content_type,
      count: row.word_count,
      name: this.getContentTypeName(row.content_type),
      description: this.getContentTypeDescription(row.content_type)
    }));
  }
  
  // 获取单元列表
  static async getUnits({ publisher, grade, semester, type }) {
    const query = `
      SELECT 
        tu.*,
        COUNT(DISTINCT tl.id) as lesson_count,
        COUNT(DISTINCT tw.id) as word_count
      FROM textbook_units tu
      LEFT JOIN textbook_lessons tl ON tu.id = tl.unit_id
      LEFT JOIN textbook_words tw ON tl.id = tw.lesson_id AND tw.content_type = ?
      WHERE tu.publisher = ? AND tu.grade = ? AND tu.semester = ?
      GROUP BY tu.id
      ORDER BY tu.unit_order
    `;
    
    const [units] = await db.execute(query, [type, publisher, grade, semester]);
    
    // 获取每个单元的课程列表
    for (let unit of units) {
      unit.lessons = await this.getUnitLessons(unit.id, type);
    }
    
    return units;
  }
  
  // 获取单元课程
  static async getUnitLessons(unitId, contentType) {
    const query = `
      SELECT 
        tl.*,
        COUNT(tw.id) as word_count,
        COALESCE(up.progress, 0) as progress
      FROM textbook_lessons tl
      LEFT JOIN textbook_words tw ON tl.id = tw.lesson_id AND tw.content_type = ?
      LEFT JOIN user_lesson_progress up ON tl.id = up.lesson_id
      WHERE tl.unit_id = ?
      GROUP BY tl.id
      ORDER BY tl.lesson_order
    `;
    
    const [lessons] = await db.execute(query, [contentType, unitId]);
    
    return lessons.map(lesson => ({
      ...lesson,
      status: this.getLessonStatus(lesson.progress)
    }));
  }
  
  // 获取课程内容
  static async getLessonContent(lessonId, contentType) {
    const query = `
      SELECT 
        tw.*,
        tl.title as lesson_title,
        tu.name as unit_name
      FROM textbook_words tw
      JOIN textbook_lessons tl ON tw.lesson_id = tl.id
      JOIN textbook_units tu ON tl.unit_id = tu.id
      WHERE tw.lesson_id = ? AND tw.content_type = ?
      ORDER BY tw.word_order
    `;
    
    const [words] = await db.execute(query, [lessonId, contentType]);
    
    return {
      lesson_id: lessonId,
      content_type: contentType,
      words: words
    };
  }
  
  // 辅助方法
  static getContentTypeName(type) {
    const names = {
      'writing': '写字表',
      'recognition': '识字表',
      'vocabulary': '词汇表'
    };
    return names[type] || type;
  }
  
  static getContentTypeDescription(type) {
    const descriptions = {
      'writing': '需要学会书写的汉字',
      'recognition': '需要认识的汉字',
      'vocabulary': '重点词汇和短语'
    };
    return descriptions[type] || '';
  }
  
  static getLessonStatus(progress) {
    if (progress >= 100) return 'completed';
    if (progress > 0) return 'in_progress';
    return 'not_started';
  }
}

module.exports = TextbookService;
```

## 6. 数据接口

### 6.1 教材内容接口

#### 获取内容类型
- **接口**: `GET /api/textbook/content-types`
- **参数**: 
  - `publisher`: 出版社ID (renjiao/bubian/beishida)
  - `grade`: 年级 (1-6)
  - `semester`: 学期 (first/second)
- **返回**:
```json
{
  "data": [
    {
      "type": "writing",
      "name": "写字表",
      "description": "需要学会书写的汉字",
      "count": 120
    },
    {
      "type": "recognition", 
      "name": "识字表",
      "description": "需要认识的汉字",
      "count": 200
    }
  ]
}
```

#### 获取单元列表
- **接口**: `GET /api/textbook/units`
- **参数**:
  - `publisher`: 出版社ID
  - `grade`: 年级
  - `semester`: 学期
  - `type`: 内容类型
- **返回**:
```json
{
  "data": [
    {
      "id": 1,
      "name": "第一单元",
      "description": "大自然的声音",
      "lesson_count": 4,
      "word_count": 32,
      "lessons": [
        {
          "id": 1,
          "title": "大青树下的小学",
          "word_count": 8,
          "progress": 100,
          "status": "completed"
        }
      ]
    }
  ]
}
```

### 6.2 自定义词库接口

#### 获取我的词库
- **接口**: `GET /api/wordbook/my-list`
- **权限**: 需要登录
- **返回**:
```json
{
  "data": [
    {
      "id": 1,
      "name": "我的专属词库",
      "description": "收集日常易错字词",
      "word_count": 50,
      "is_public": false,
      "like_count": 0,
      "view_count": 0,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### 创建词库
- **接口**: `POST /api/wordbook/create`
- **权限**: 需要登录
- **参数**:
```json
{
  "name": "词库名称",
  "description": "词库描述",
  "is_public": false,
  "words": [
    {
      "word": "苹果",
      "pinyin": "píng guǒ",
      "definition": "一种水果"
    }
  ]
}
```

## 7. 测试用例

### 7.1 功能测试

#### 教材选择测试
1. **正常流程测试**
   - 依次选择出版社、年级、学期
   - 验证每步选择后的状态更新
   - 验证最终跳转到内容选择页面

2. **边界条件测试**
   - 未完成选择时点击下一步
   - 网络异常时的错误处理
   - 数据加载失败的重试机制

3. **用户偏好测试**
   - 验证用户选择的保存和加载
   - 验证重新进入页面时的状态恢复

#### 自定义词库测试
1. **创建词库测试**
   - 正常创建流程
   - 超出数量限制的处理
   - 内容格式验证

2. **编辑词库测试**
   - 修改词库信息
   - 添加/删除词条
   - 权限验证

3. **分享功能测试**
   - 设置词库为公开
   - 复制他人词库
   - 分享链接生成

### 7.2 性能测试

#### 数据加载性能
- 教材内容加载时间 < 2秒
- 词库列表加载时间 < 1秒
- 搜索响应时间 < 500ms

#### 存储空间测试
- 本地缓存大小控制
- 图片资源优化
- 数据同步策略

### 7.3 兼容性测试

#### 设备兼容
- 不同尺寸屏幕适配
- 不同版本微信兼容
- 性能较低设备的流畅度

#### 网络环境测试
- 弱网环境下的用户体验
- 离线模式的基本功能
- 网络切换时的状态保持

这个设计文档涵盖了字库内容选择功能的完整设计，包括教材同步和自定义词库两大核心模块，提供了详细的实现方案和测试策略。 