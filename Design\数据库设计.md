# 小学字词听写软件数据库设计

## 1. 数据库概述

### 1.1 设计原则
- **规范化设计**：遵循第三范式，避免数据冗余
- **性能优化**：合理设置索引，优化查询效率
- **扩展性**：预留扩展字段，支持功能迭代
- **数据完整性**：设置外键约束，保证数据一致性

### 1.2 技术规范
- **数据库类型**：MySQL 8.0
- **字符编码**：UTF8MB4（支持表情符号）
- **存储引擎**：InnoDB（支持事务和外键）
- **命名规范**：下划线命名法，表名复数形式

## 2. 数据库架构

### 2.1 数据库分类
```
wordapp_db
├── 用户相关表
│   ├── users (用户表)
│   ├── user_profiles (用户档案表)
│   └── user_achievements (用户成就表)
├── 教材内容表
│   ├── publishers (出版社表)
│   ├── textbooks (教材表)
│   ├── grades (年级表)
│   ├── terms (学期表)
│   ├── units (单元表)
│   ├── lessons (课程表)
│   └── words (字词表)
├── 练习相关表
│   ├── practice_sessions (练习会话表)
│   ├── practice_records (练习记录表)
│   └── practice_scores (评分记录表)
├── 错题管理表
│   ├── wrong_words (错题表)
│   └── review_plans (复习计划表)
├── 成就系统表
│   ├── achievements (成就定义表)
│   ├── user_points (用户积分表)
│   └── leaderboards (排行榜表)
└── 系统配置表
    ├── app_configs (应用配置表)
    └── operation_logs (操作日志表)
```

## 3. 详细表结构设计

### 3.1 用户相关表

#### 3.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    openid VARCHAR(50) UNIQUE NOT NULL COMMENT '微信openid',
    unionid VARCHAR(50) COMMENT '微信unionid',
    nickname VARCHAR(50) NOT NULL COMMENT '用户昵称',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0未知，1男，2女',
    city VARCHAR(50) COMMENT '城市',
    province VARCHAR(50) COMMENT '省份',
    country VARCHAR(50) COMMENT '国家',
    status TINYINT DEFAULT 1 COMMENT '状态：0禁用，1正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP COMMENT '最后登录时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 索引
CREATE INDEX idx_openid ON users(openid);
CREATE INDEX idx_status ON users(status);
CREATE INDEX idx_created_at ON users(created_at);
```

#### 3.1.2 用户档案表 (user_profiles)
```sql
CREATE TABLE user_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '档案ID',
    user_id INT NOT NULL COMMENT '用户ID',
    grade_id INT COMMENT '年级ID',
    school_name VARCHAR(100) COMMENT '学校名称',
    class_name VARCHAR(50) COMMENT '班级',
    current_textbook_id INT COMMENT '当前使用教材ID',
    total_practice_time INT DEFAULT 0 COMMENT '总练习时长(分钟)',
    total_practice_count INT DEFAULT 0 COMMENT '总练习次数',
    total_words_practiced INT DEFAULT 0 COMMENT '总练习字词数',
    current_level INT DEFAULT 1 COMMENT '当前等级',
    total_points INT DEFAULT 0 COMMENT '总积分',
    title VARCHAR(50) DEFAULT '小学徒' COMMENT '当前称号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户档案表';

-- 索引
CREATE INDEX idx_user_id ON user_profiles(user_id);
CREATE INDEX idx_grade_id ON user_profiles(grade_id);
CREATE INDEX idx_total_points ON user_profiles(total_points);
```

### 3.2 教材内容表

#### 3.2.1 出版社表 (publishers)
```sql
CREATE TABLE publishers (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '出版社ID',
    name VARCHAR(50) NOT NULL COMMENT '出版社名称',
    code VARCHAR(20) UNIQUE NOT NULL COMMENT '出版社代码',
    description TEXT COMMENT '描述',
    logo_url VARCHAR(255) COMMENT 'Logo URL',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：0禁用，1启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出版社表';

-- 初始数据
INSERT INTO publishers (name, code, description) VALUES
('人民教育出版社', 'PEP', '人教版教材'),
('北京师范大学出版社', 'BNU', '北师大版教材'),  
('部编版', 'BUBIAN', '统编语文教材');
```

#### 3.2.2 年级表 (grades)
```sql
CREATE TABLE grades (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '年级ID',
    name VARCHAR(20) NOT NULL COMMENT '年级名称',
    level INT NOT NULL COMMENT '年级数字',
    description VARCHAR(100) COMMENT '描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='年级表';

-- 初始数据
INSERT INTO grades (name, level, sort_order) VALUES
('一年级', 1, 1), ('二年级', 2, 2), ('三年级', 3, 3),
('四年级', 4, 4), ('五年级', 5, 5), ('六年级', 6, 6);
```

#### 3.2.3 教材表 (textbooks)
```sql
CREATE TABLE textbooks (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '教材ID',
    publisher_id INT NOT NULL COMMENT '出版社ID',
    grade_id INT NOT NULL COMMENT '年级ID',
    name VARCHAR(100) NOT NULL COMMENT '教材名称',
    version VARCHAR(50) COMMENT '版本号',
    cover_url VARCHAR(255) COMMENT '封面图片URL',
    description TEXT COMMENT '教材描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0禁用，1启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (publisher_id) REFERENCES publishers(id),
    FOREIGN KEY (grade_id) REFERENCES grades(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材表';

-- 索引
CREATE INDEX idx_publisher_grade ON textbooks(publisher_id, grade_id);
```

#### 3.2.4 学期表 (terms)
```sql
CREATE TABLE terms (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '学期ID',
    textbook_id INT NOT NULL COMMENT '教材ID',
    name VARCHAR(20) NOT NULL COMMENT '学期名称',
    term_number TINYINT NOT NULL COMMENT '学期序号：1上学期，2下学期',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (textbook_id) REFERENCES textbooks(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学期表';
```

#### 3.2.5 单元表 (units)
```sql
CREATE TABLE units (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '单元ID',
    term_id INT NOT NULL COMMENT '学期ID',
    name VARCHAR(50) NOT NULL COMMENT '单元名称',
    unit_number INT NOT NULL COMMENT '单元序号',
    description TEXT COMMENT '单元描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (term_id) REFERENCES terms(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单元表';
```

#### 3.2.6 课程表 (lessons)
```sql
CREATE TABLE lessons (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '课程ID',
    unit_id INT NOT NULL COMMENT '单元ID',
    name VARCHAR(100) NOT NULL COMMENT '课程名称',
    lesson_number INT NOT NULL COMMENT '课程序号',
    content_type ENUM('text', 'poem', 'story') DEFAULT 'text' COMMENT '内容类型',
    description TEXT COMMENT '课程描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程表';
```

#### 3.2.7 字词表 (words)
```sql
CREATE TABLE words (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '字词ID',
    lesson_id INT NOT NULL COMMENT '课程ID',
    word VARCHAR(10) NOT NULL COMMENT '字或词',
    pinyin VARCHAR(50) COMMENT '拼音',
    word_type ENUM('character', 'word', 'phrase') NOT NULL COMMENT '类型：字符、词语、短语',
    category ENUM('writing', 'recognition', 'vocabulary') NOT NULL COMMENT '分类：写字表、识字表、词汇表',
    meaning TEXT COMMENT '释义',
    example_sentence TEXT COMMENT '例句',
    audio_url VARCHAR(255) COMMENT '音频文件URL',
    stroke_count INT COMMENT '笔画数',
    difficulty_level TINYINT DEFAULT 1 COMMENT '难度等级1-5',
    frequency_rank INT COMMENT '使用频率排名',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (lesson_id) REFERENCES lessons(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字词表';

-- 索引
CREATE INDEX idx_lesson_category ON words(lesson_id, category);
CREATE INDEX idx_word_type ON words(word_type);
CREATE INDEX idx_difficulty ON words(difficulty_level);
```

### 3.3 练习相关表

#### 3.3.1 练习会话表 (practice_sessions)
```sql
CREATE TABLE practice_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    session_type ENUM('all', 'category', 'single') NOT NULL COMMENT '练习类型',
    content_selection JSON COMMENT '内容选择配置',
    total_words INT DEFAULT 0 COMMENT '总字词数',
    completed_words INT DEFAULT 0 COMMENT '已完成字词数',
    correct_count INT DEFAULT 0 COMMENT '正确数量',
    wrong_count INT DEFAULT 0 COMMENT '错误数量',
    accuracy_rate DECIMAL(5,2) COMMENT '准确率',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    duration_minutes INT COMMENT '练习时长(分钟)',
    status ENUM('ongoing', 'completed', 'interrupted') DEFAULT 'ongoing' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='练习会话表';

-- 索引
CREATE INDEX idx_user_status ON practice_sessions(user_id, status);
CREATE INDEX idx_start_time ON practice_sessions(start_time);
```

#### 3.3.2 练习记录表 (practice_records)
```sql
CREATE TABLE practice_records (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    session_id INT NOT NULL COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    word_id INT NOT NULL COMMENT '字词ID',
    user_input VARCHAR(10) COMMENT '用户输入',
    is_correct TINYINT NOT NULL COMMENT '是否正确：0错误，1正确',
    attempts INT DEFAULT 1 COMMENT '尝试次数',
    time_spent INT COMMENT '用时(秒)',
    handwriting_data TEXT COMMENT '手写数据',
    recognition_result VARCHAR(10) COMMENT '识别结果',
    confidence_score DECIMAL(5,2) COMMENT '识别置信度',
    error_type ENUM('stroke', 'structure', 'recognition', 'other') COMMENT '错误类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (session_id) REFERENCES practice_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (word_id) REFERENCES words(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='练习记录表';

-- 索引
CREATE INDEX idx_session_word ON practice_records(session_id, word_id);
CREATE INDEX idx_user_correct ON practice_records(user_id, is_correct);
CREATE INDEX idx_created_at ON practice_records(created_at);
```

### 3.4 错题管理表

#### 3.4.1 错题表 (wrong_words)
```sql
CREATE TABLE wrong_words (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '错题ID',
    user_id INT NOT NULL COMMENT '用户ID',
    word_id INT NOT NULL COMMENT '字词ID',
    wrong_count INT DEFAULT 1 COMMENT '错误次数',
    last_wrong_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后错误时间',
    error_types JSON COMMENT '错误类型统计',
    mastery_level TINYINT DEFAULT 0 COMMENT '掌握程度：0未掌握，1-5逐步掌握',
    review_count INT DEFAULT 0 COMMENT '复习次数',
    last_review_time TIMESTAMP NULL COMMENT '最后复习时间',
    next_review_time TIMESTAMP NULL COMMENT '下次复习时间',
    is_cleared TINYINT DEFAULT 0 COMMENT '是否已清除：0未清除，1已清除',
    cleared_time TIMESTAMP NULL COMMENT '清除时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (word_id) REFERENCES words(id),
    UNIQUE KEY uk_user_word (user_id, word_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错题表';

-- 索引
CREATE INDEX idx_user_mastery ON wrong_words(user_id, mastery_level);
CREATE INDEX idx_next_review ON wrong_words(next_review_time);
CREATE INDEX idx_is_cleared ON wrong_words(is_cleared);
```

### 3.5 成就系统表

#### 3.5.1 成就定义表 (achievements)
```sql
CREATE TABLE achievements (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '成就ID',
    name VARCHAR(50) NOT NULL COMMENT '成就名称',
    description TEXT COMMENT '成就描述',
    icon_url VARCHAR(255) COMMENT '图标URL',
    category ENUM('practice', 'accuracy', 'streak', 'time', 'special') NOT NULL COMMENT '成就分类',
    condition_type ENUM('count', 'rate', 'streak', 'time') NOT NULL COMMENT '条件类型',
    condition_value INT NOT NULL COMMENT '条件数值',
    reward_points INT DEFAULT 0 COMMENT '奖励积分',
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成就定义表';

-- 初始数据示例
INSERT INTO achievements (name, description, category, condition_type, condition_value, reward_points, rarity) VALUES
('初出茅庐', '完成第一次练习', 'practice', 'count', 1, 10, 'common'),
('百发百中', '单次练习准确率达到100%', 'accuracy', 'rate', 100, 50, 'rare'),
('勤学苦练', '累计练习100个字词', 'practice', 'count', 100, 100, 'common'),
('一气呵成', '连续正确答对50个字词', 'streak', 'streak', 50, 200, 'epic');
```

#### 3.5.2 用户成就表 (user_achievements)
```sql
CREATE TABLE user_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id INT NOT NULL COMMENT '用户ID',
    achievement_id INT NOT NULL COMMENT '成就ID',
    achieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    is_displayed TINYINT DEFAULT 1 COMMENT '是否展示',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES achievements(id),
    UNIQUE KEY uk_user_achievement (user_id, achievement_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户成就表';
```

#### 3.5.3 用户积分表 (user_points)
```sql
CREATE TABLE user_points (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id INT NOT NULL COMMENT '用户ID',
    points_change INT NOT NULL COMMENT '积分变化',
    current_points INT NOT NULL COMMENT '当前总积分',
    source_type ENUM('practice', 'achievement', 'daily', 'bonus') NOT NULL COMMENT '来源类型',
    source_id INT COMMENT '来源ID',
    description VARCHAR(100) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分记录表';

-- 索引
CREATE INDEX idx_user_created ON user_points(user_id, created_at);
CREATE INDEX idx_source ON user_points(source_type, source_id);
```

### 3.6 系统配置表

#### 3.6.1 应用配置表 (app_configs)
```sql
CREATE TABLE app_configs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    category VARCHAR(30) COMMENT '配置分类',
    data_type ENUM('string', 'int', 'json', 'boolean') DEFAULT 'string' COMMENT '数据类型',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用配置表';

-- 初始配置数据
INSERT INTO app_configs (config_key, config_value, description, category, data_type) VALUES
('handwriting_api_url', 'https://api.example.com/ocr', '手写识别API地址', 'api', 'string'),
('min_confidence_score', '0.85', '最低识别置信度', 'recognition', 'string'),
('max_practice_words', '50', '单次练习最大字词数', 'practice', 'int'),
('point_rules', '{"correct": 10, "streak_bonus": 5}', '积分规则配置', 'points', 'json');
```

#### 3.6.2 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id INT COMMENT '用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(200) COMMENT '操作描述',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 索引
CREATE INDEX idx_user_operation ON operation_logs(user_id, operation_type);
CREATE INDEX idx_created_at ON operation_logs(created_at);
```

## 4. 数据库关系图

```
users (用户表)
├── user_profiles (1:1) 用户档案
├── practice_sessions (1:N) 练习会话
├── practice_records (1:N) 练习记录
├── wrong_words (1:N) 错题记录
├── user_achievements (1:N) 用户成就
├── user_points (1:N) 积分记录
└── operation_logs (1:N) 操作日志

publishers (出版社) → textbooks (教材) → terms (学期) → units (单元) → lessons (课程) → words (字词)
                                    ↓
                            practice_records (练习记录)
                                    ↓
                              wrong_words (错题)

achievements (成就定义) → user_achievements (用户成就)
```

## 5. 索引优化策略

### 5.1 主要查询场景
- 用户登录验证：`users.openid`
- 练习记录查询：`practice_records(user_id, created_at)`
- 错题统计：`wrong_words(user_id, is_cleared)`
- 教材内容查询：`words(lesson_id, category)`
- 排行榜查询：`user_profiles.total_points`

### 5.2 复合索引设计
```sql
-- 练习记录复合索引
CREATE INDEX idx_practice_user_time ON practice_records(user_id, created_at, is_correct);

-- 错题复合索引  
CREATE INDEX idx_wrong_user_mastery_time ON wrong_words(user_id, mastery_level, next_review_time);

-- 字词复合索引
CREATE INDEX idx_words_lesson_category_type ON words(lesson_id, category, word_type);

-- 成就查询索引
CREATE INDEX idx_achievement_category_active ON achievements(category, is_active);
```

## 6. 数据备份与维护

### 6.1 备份策略
- **全量备份**：每日凌晨3点自动备份
- **增量备份**：每小时一次增量备份
- **备份保留**：全量备份保留30天，增量备份保留7天
- **异地备份**：重要数据异地灾备

### 6.2 数据清理
- **日志清理**：操作日志保留90天
- **临时数据**：未完成的练习会话30天后清理
- **无效数据**：软删除数据定期归档

### 6.3 性能监控
- **慢查询监控**：记录执行时间超过2秒的查询
- **索引优化**：定期分析索引使用情况
- **存储空间**：监控表空间使用率
- **连接数监控**：数据库连接数预警 