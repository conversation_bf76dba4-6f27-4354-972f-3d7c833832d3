# 错题集功能设计

## 1. 功能描述

错题集是帮助学生巩固薄弱知识点的重要功能，通过智能收集、分类管理和针对性练习，提高学习效率。

### 1.1 核心功能
- **自动收集**：自动收集听写练习中的错误字词
- **分类管理**：按错误类型、时间、难度分类
- **针对性练习**：为错题提供专项练习
- **学习跟踪**：记录错题的掌握进度

### 1.2 用户价值
- 精准识别学习薄弱点
- 提供个性化复习方案
- 避免重复犯错
- 提高学习效率

## 2. 实现流程

### 2.1 错题收集流程
```
听写练习 → 答题错误 → 自动分析错误类型 → 加入错题集 → 智能分类 → 推荐练习
```

### 2.2 错题练习流程
```
选择错题类型 → 生成练习计划 → 开始专项练习 → 记录练习结果 → 更新掌握状态 → 调整复习频率
```

### 2.3 错题清理流程
```
检测掌握程度 → 连续正确次数达标 → 提示清理 → 用户确认 → 移入历史错题 → 释放主错题集空间
```

## 3. 业务规则

### 3.1 错题分类规则
- **字形相似**：如"影"写成"景"
- **笔画错误**：笔画数量或顺序错误
- **结构问题**：偏旁部首位置错误
- **音形混淆**：同音字混用

### 3.2 掌握度判定规则
- **未掌握**：错误次数 > 正确次数
- **基本掌握**：连续正确2次
- **熟练掌握**：连续正确5次
- **完全掌握**：30天内连续正确10次

### 3.3 复习提醒规则
- **艾宾浩斯遗忘曲线**：1天、3天、7天、15天、30天
- **错误频率**：高频错误优先复习
- **重要程度**：教材重点字词优先

## 4. 界面设计要求

### 4.1 错题集主页面
```
┌─────────────────────────────────────────┐
│ ←  我的错题集                  📊 统计   │
├─────────────────────────────────────────┤
│                                        │
│  📈 学习概况                            │
│  ┌─────────────────────────────────────┐ │
│  │  待清理：15个  已掌握：28个          │ │
│  │  本周新增：3个  清理率：65%          │ │
│  │  ████████████░░░░░░░░               │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🎯 快速练习                            │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ 📝 全部  │ │ ⚡ 高频  │ │ 🔥 新增  │   │
│  │   15个   │ │   8个   │ │   3个   │   │
│  └─────────┘ └─────────┘ └─────────┘   │
│                                        │
│  📚 分类管理                            │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 🔍 字形相似 (6个)                 > │ │
│  │ 最近错误：影→景，暖→爱              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ✏️ 笔画错误 (4个)                 > │ │
│  │ 最近错误：练→炼，组→粗              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 🏗️ 结构问题 (3个)                 > │ │
│  │ 最近错误：明→朋，想→相              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 🔊 音形混淆 (2个)                 > │ │
│  │ 最近错误：做→作，再→在              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📜 历史错题                            │
│  ┌─────────────────────────────────────┐ │
│  │ 📖 已掌握错题 (28个)             > │ │
│  │ 恭喜！这些字词你已经完全掌握了        │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 4.2 错题详情页面
```
┌─────────────────────────────────────────┐
│ ←  字形相似错题                        │
├─────────────────────────────────────────┤
│                                        │
│  📝 共6个错题字词                       │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 影 → 景                            │ │
│  │ ┌───────┐ 错误3次  最近：2天前      │ │
│  │ │ 未掌握 │ 正确1次  首次：1周前      │ │
│  │ └───────┘                          │ │
│  │                                    │ │
│  │ 📖 来源：三年级上册-第1课           │ │
│  │ 🔍 分析：两字字形相似，注意区分      │ │
│  │                                    │ │
│  │ ┌────┐ ┌────┐ ┌────┐ ┌────────┐  │ │
│  │ │练习│ │详情│ │删除│ │ 标记掌握 │  │ │
│  │ └────┘ └────┘ └────┘ └────────┘  │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ 暖 → 爱                            │ │
│  │ ┌───────┐ 错误2次  最近：1天前      │ │
│  │ │ 基本掌握│ 正确2次  首次：3天前      │ │
│  │ └───────┘                          │ │
│  │                                    │ │
│  │ 📖 来源：三年级上册-第5课           │ │
│  │ 🔍 分析：注意"暖"的右边是"爱"        │ │
│  │                                    │ │
│  │ ┌────┐ ┌────┐ ┌────┐ ┌────────┐  │ │
│  │ │练习│ │详情│ │删除│ │ 标记掌握 │  │ │
│  │ └────┘ └────┘ └────┘ └────────┘  │ │
│  └─────────────────────────────────────┘ │
│                                        │
│                              ┌───────┐ │
│                              │ 批量练习│ │
│                              └───────┘ │
└─────────────────────────────────────────┘
```

### 4.3 错题统计页面
```
┌─────────────────────────────────────────┐
│ ←  错题统计                            │
├─────────────────────────────────────────┤
│                                        │
│  📊 总体数据                            │
│  ┌─────────────────────────────────────┐ │
│  │          错题总数：43个             │ │
│  │         已掌握：28个 (65%)          │ │
│  │         待清理：15个 (35%)          │ │
│  │                                    │ │
│  │    █████████████░░░░░░░░            │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📈 错误类型分布                        │
│  ┌─────────────────────────────────────┐ │
│  │  字形相似  ████████████ 40% (6个)   │ │
│  │  笔画错误  ████████░░░░ 27% (4个)   │ │
│  │  结构问题  ██████░░░░░░ 20% (3个)   │ │
│  │  音形混淆  ████░░░░░░░░ 13% (2个)   │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📅 近期趋势                            │
│  ┌─────────────────────────────────────┐ │
│  │           新增错题数量               │ │
│  │  5 ┌─┐                             │ │
│  │  4 │ │   ┌─┐                       │ │
│  │  3 │ │   │ │ ┌─┐                   │ │
│  │  2 │ │   │ │ │ │     ┌─┐           │ │
│  │  1 │ │ ┌─┤ │ │ │   ┌─┤ │           │ │
│  │  0 └─┴─┴─┴─┴─┴─┴─┬─┴─┴─┴──┬──────── │ │
│  │    一二三四五六日  本周  上周        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  🎯 掌握率排行                          │
│  ┌─────────────────────────────────────┐ │
│  │  1. 笔画错误      85% ████████░░    │ │
│  │  2. 结构问题      78% ███████░░░    │ │
│  │  3. 音形混淆      65% ██████░░░░    │ │
│  │  4. 字形相似      45% ████░░░░░░    │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📚 教材分布                            │
│  ┌─────────────────────────────────────┐ │
│  │  三年级上册     █████████ 60% (9个) │ │
│  │  三年级下册     ██████░░░ 40% (6个) │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 4.4 错题练习页面
```
┌─────────────────────────────────────────┐
│ ←  错题专项练习            ⏸️ 暂停       │
├─────────────────────────────────────────┤
│                                        │
│  📊 练习进度：3/6           🎯 错题专练  │
│  ████████████░░░░░░░░ 50%               │
│                                        │
│  ⚠️ 这是你的错题字词                    │
│  ┌─────────────────────────────────────┐ │
│  │               影                    │ │
│  │          [yǐng]                    │ │
│  │                                    │ │
│  │  📝 之前错误：写成了"景"             │ │
│  │  💡 记忆要点：左边是"彡"不是"日"      │ │
│  │                                    │ │
│  │     🔊 ▶️ 播放  🔄 重播             │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ✏️ 请仔细书写，注意字形区别              │
│  ┌─────────────────────────────────────┐ │
│  │             田字格书写区             │ │
│  │                                    │ │
│  │        ┌─────────┐                 │ │
│  │        │         │                 │ │
│  │        │   影    │                 │ │
│  │        │ ┼   ┼   │                 │ │
│  │        │ ─   ─   │                 │ │
│  │        │ ┼   ┼   │                 │ │
│  │        └─────────┘                 │ │
│  │                                    │ │
│  │  🎯 识别结果：影 (98% 置信度) ✅     │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  📚 对比学习                            │
│  ┌─────────────────────────────────────┐ │
│  │    正确：影        错误：景          │ │
│  │   ┌───────┐     ┌───────┐           │ │
│  │   │   影  │     │   景  │           │ │
│  │   └───────┘     └───────┘           │ │
│  │   左边"彡"      左边"日"              │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │   ✏️    │ │   🗑️    │ │   ✅    │   │
│  │   重写   │ │   清除   │ │   下一题 │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────┘
```

### 4.5 清理建议页面
```
┌─────────────────────────────────────────┐
│ ←  清理建议                            │
├─────────────────────────────────────────┤
│                                        │
│  🎉 恭喜！你有新的掌握字词               │
│                                        │
│  ✅ 建议清理的字词 (3个)                │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ☑️ 暖                              │ │
│  │     连续正确 5次，已完全掌握         │ │
│  │     最后练习：3天前                 │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ☑️ 粗                              │ │
│  │     连续正确 8次，已完全掌握         │ │
│  │     最后练习：1周前                 │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ☑️ 练                              │ │
│  │     连续正确 6次，已完全掌握         │ │
│  │     最后练习：5天前                 │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  💡 清理说明                            │
│  ┌─────────────────────────────────────┐ │
│  │  • 清理后将移入"已掌握"区域          │ │
│  │  • 可随时在历史记录中查看           │ │
│  │  • 系统会继续监测掌握情况           │ │
│  │  • 如再次出错会重新加入错题集        │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────────────────────────────────┐ │
│  │ ☑️ 全选                            │ │
│  └─────────────────────────────────────┘ │
│                                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ 📝 再练习 │ │ ❌ 取消  │ │ ✅ 确认  │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────┘
```

## 5. 技术实现

### 5.1 前端实现（微信小程序）

#### 错题集主页面
```javascript
// pages/error-collection/index.js
Page({
  data: {
    summary: {
      totalErrors: 0,
      masteredCount: 0,
      pendingCount: 0,
      weeklyNew: 0,
      masteryRate: 0
    },
    errorTypes: [
      { type: 'similar_shape', name: '字形相似', count: 0, icon: '🔍' },
      { type: 'stroke_error', name: '笔画错误', count: 0, icon: '✏️' },
      { type: 'structure_error', name: '结构问题', count: 0, icon: '🏗️' },
      { type: 'phonetic_confusion', name: '音形混淆', count: 0, icon: '🔊' }
    ],
    recentErrors: [],
    loading: false
  },

  onLoad() {
    this.loadErrorSummary();
    this.loadErrorTypes();
    this.checkClearSuggestions();
  },

  onShow() {
    this.loadErrorSummary();
  },

  // 加载错题概况
  async loadErrorSummary() {
    try {
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/error-collection/summary`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      this.setData({ summary: res.data });
    } catch (error) {
      console.error('加载概况失败:', error);
    }
  },

  // 加载错误类型统计
  async loadErrorTypes() {
    try {
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/error-collection/types`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      this.setData({ errorTypes: res.data });
    } catch (error) {
      console.error('加载类型统计失败:', error);
    }
  },

  // 检查清理建议
  async checkClearSuggestions() {
    try {
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/error-collection/clear-suggestions`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      if (res.data.length > 0) {
        this.showClearDialog(res.data);
      }
    } catch (error) {
      console.error('检查清理建议失败:', error);
    }
  },

  // 显示清理对话框
  showClearDialog(suggestions) {
    const words = suggestions.map(s => s.word).join('、');
    wx.showModal({
      title: '清理建议',
      content: `恭喜！你已掌握：${words}。是否移入已掌握区域？`,
      confirmText: '查看详情',
      cancelText: '稍后处理',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/clear-suggestions/index'
          });
        }
      }
    });
  },

  // 快速练习
  onQuickPractice(e) {
    const type = e.currentTarget.dataset.type;
    
    wx.navigateTo({
      url: `/pages/error-practice/index?type=${type}`
    });
  },

  // 查看错误类型详情
  onViewErrorType(e) {
    const type = e.currentTarget.dataset.type;
    
    wx.navigateTo({
      url: `/pages/error-detail/index?type=${type}`
    });
  },

  // 查看统计
  onViewStatistics() {
    wx.navigateTo({
      url: '/pages/error-statistics/index'
    });
  },

  // 查看历史错题
  onViewHistory() {
    wx.navigateTo({
      url: '/pages/error-history/index'
    });
  }
});
```

#### 错题练习页面
```javascript
// pages/error-practice/index.js
Page({
  data: {
    errorWords: [],
    currentIndex: 0,
    currentWord: null,
    practiceMode: 'error', // 错题专练模式
    showComparison: false,
    userAnswer: '',
    isCorrect: false,
    practiceResults: []
  },

  onLoad(options) {
    const { type, ids } = options;
    this.loadErrorWords(type, ids);
  },

  // 加载错题字词
  async loadErrorWords(type, ids) {
    try {
      wx.showLoading({ title: '准备中...' });
      
      const res = await wx.request({
        url: `${app.globalData.apiUrl}/error-collection/practice-words`,
        method: 'GET',
        data: { type, ids },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });

      const errorWords = res.data;
      this.setData({
        errorWords,
        currentWord: errorWords[0],
        showComparison: true // 错题练习显示对比
      });

      this.startErrorPractice();
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },

  // 开始错题练习
  startErrorPractice() {
    // 播放当前字词
    this.playCurrentWord();
    
    // 显示错误提示
    this.showErrorHint();
  },

  // 显示错误提示
  showErrorHint() {
    const { currentWord } = this.data;
    const errorInfo = currentWord.error_info;
    
    wx.showToast({
      title: `注意：${errorInfo.hint}`,
      icon: 'none',
      duration: 3000
    });
  },

  // 处理答题结果
  onAnswerSubmit(userAnswer) {
    const { currentWord } = this.data;
    const isCorrect = userAnswer === currentWord.word;
    
    this.setData({
      userAnswer,
      isCorrect
    });

    // 记录练习结果
    this.recordPracticeResult(isCorrect);
    
    // 更新错题状态
    this.updateErrorStatus(isCorrect);
    
    // 显示结果反馈
    this.showResultFeedback(isCorrect);
    
    // 准备下一题
    setTimeout(() => {
      this.nextWord();
    }, 2000);
  },

  // 记录练习结果
  recordPracticeResult(isCorrect) {
    const result = {
      word: this.data.currentWord.word,
      userAnswer: this.data.userAnswer,
      isCorrect,
      timestamp: Date.now(),
      practiceType: 'error_correction'
    };
    
    this.data.practiceResults.push(result);
  },

  // 更新错题状态
  async updateErrorStatus(isCorrect) {
    try {
      await wx.request({
        url: `${app.globalData.apiUrl}/error-collection/update-status`,
        method: 'POST',
        data: {
          errorId: this.data.currentWord.error_id,
          isCorrect,
          timestamp: Date.now()
        },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });
    } catch (error) {
      console.error('更新错题状态失败:', error);
    }
  },

  // 显示结果反馈
  showResultFeedback(isCorrect) {
    if (isCorrect) {
      wx.showToast({
        title: '很好！继续保持',
        icon: 'success',
        duration: 2000
      });
    } else {
      const { currentWord } = this.data;
      wx.showToast({
        title: `还需努力，正确答案：${currentWord.word}`,
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 下一题
  nextWord() {
    const { currentIndex, errorWords } = this.data;
    
    if (currentIndex + 1 < errorWords.length) {
      const nextIndex = currentIndex + 1;
      this.setData({
        currentIndex: nextIndex,
        currentWord: errorWords[nextIndex],
        userAnswer: '',
        isCorrect: false
      });
      
      this.startErrorPractice();
    } else {
      this.finishErrorPractice();
    }
  },

  // 完成错题练习
  finishErrorPractice() {
    const { practiceResults } = this.data;
    const correctCount = practiceResults.filter(r => r.isCorrect).length;
    const totalCount = practiceResults.length;
    
    // 保存练习记录
    this.savePracticeRecord();
    
    // 跳转到结果页面
    wx.redirectTo({
      url: `/pages/practice-result/index?mode=error&correct=${correctCount}&total=${totalCount}`
    });
  },

  // 保存练习记录
  async savePracticeRecord() {
    try {
      await wx.request({
        url: `${app.globalData.apiUrl}/practice/save-error-practice`,
        method: 'POST',
        data: {
          results: this.data.practiceResults,
          practiceType: 'error_correction'
        },
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        }
      });
    } catch (error) {
      console.error('保存练习记录失败:', error);
    }
  }
});
```

### 5.2 后端实现（Node.js）

#### 错题集API
```javascript
// routes/error-collection.js
const express = require('express');
const router = express.Router();
const ErrorCollectionService = require('../services/error-collection');
const auth = require('../middleware/auth');

// 获取错题概况
router.get('/summary', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const summary = await ErrorCollectionService.getSummary(userId);
    
    res.json(summary);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取错误类型统计
router.get('/types', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const types = await ErrorCollectionService.getErrorTypes(userId);
    
    res.json(types);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 添加错题
router.post('/add', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const errorData = req.body;
    
    const result = await ErrorCollectionService.addError(userId, errorData);
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 更新错题状态
router.post('/update-status', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { errorId, isCorrect, timestamp } = req.body;
    
    const result = await ErrorCollectionService.updateErrorStatus(
      userId, errorId, isCorrect, timestamp
    );
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 获取清理建议
router.get('/clear-suggestions', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const suggestions = await ErrorCollectionService.getClearSuggestions(userId);
    
    res.json(suggestions);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 清理错题
router.post('/clear', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { errorIds } = req.body;
    
    const result = await ErrorCollectionService.clearErrors(userId, errorIds);
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

#### 错题分析服务
```javascript
// services/error-analysis.js
class ErrorAnalysisService {
  // 分析错误类型
  static analyzeErrorType(correct, userAnswer) {
    if (!userAnswer || userAnswer.trim() === '') {
      return { type: 'no_answer', confidence: 1.0 };
    }

    // 字形相似性分析
    const shapeScore = this.calculateShapeSimilarity(correct, userAnswer);
    if (shapeScore > 0.7) {
      return { 
        type: 'similar_shape', 
        confidence: shapeScore,
        details: { similar_chars: [userAnswer] }
      };
    }

    // 笔画错误分析
    const strokeDiff = this.calculateStrokeDifference(correct, userAnswer);
    if (strokeDiff.similarity > 0.6) {
      return {
        type: 'stroke_error',
        confidence: strokeDiff.similarity,
        details: strokeDiff
      };
    }

    // 结构问题分析
    const structureScore = this.analyzeStructure(correct, userAnswer);
    if (structureScore > 0.5) {
      return {
        type: 'structure_error',
        confidence: structureScore,
        details: { structure_issue: true }
      };
    }

    // 音形混淆分析
    const phoneticScore = this.analyzePhoneticConfusion(correct, userAnswer);
    if (phoneticScore > 0.8) {
      return {
        type: 'phonetic_confusion',
        confidence: phoneticScore,
        details: { confused_with: userAnswer }
      };
    }

    // 其他错误
    return {
      type: 'other_error',
      confidence: 0.5,
      details: { user_answer: userAnswer }
    };
  }

  // 计算字形相似度
  static calculateShapeSimilarity(char1, char2) {
    // 使用字符编码差异计算相似度
    const code1 = char1.charCodeAt(0);
    const code2 = char2.charCodeAt(0);
    const diff = Math.abs(code1 - code2);
    
    // 相似字符通常编码接近
    if (diff < 100) {
      return 1 - (diff / 100);
    }
    
    return 0;
  }

  // 计算笔画差异
  static calculateStrokeDifference(char1, char2) {
    const strokes1 = this.getCharacterStrokes(char1);
    const strokes2 = this.getCharacterStrokes(char2);
    
    const diff = Math.abs(strokes1 - strokes2);
    const similarity = Math.max(0, 1 - diff / Math.max(strokes1, strokes2));
    
    return {
      correct_strokes: strokes1,
      user_strokes: strokes2,
      difference: diff,
      similarity: similarity
    };
  }

  // 获取字符笔画数（简化实现）
  static getCharacterStrokes(char) {
    // 这里应该使用专业的汉字笔画数据库
    // 简化实现：根据Unicode范围估算
    const code = char.charCodeAt(0);
    
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 常用汉字，估算笔画数
      return Math.floor((code - 0x4E00) / 1000) + 3;
    }
    
    return 5; // 默认值
  }

  // 分析结构问题
  static analyzeStructure(correct, userAnswer) {
    // 简化实现：检查是否为结构相似的字符
    const structuralSimilars = {
      '明': ['朋', '胡'],
      '想': ['相', '香'],
      '影': ['景', '京']
    };
    
    if (structuralSimilars[correct] && 
        structuralSimilars[correct].includes(userAnswer)) {
      return 0.9;
    }
    
    return 0;
  }

  // 分析音形混淆
  static analyzePhoneticConfusion(correct, userAnswer) {
    // 简化实现：检查同音字
    const homophones = {
      '做': ['作'],
      '再': ['在'],
      '的': ['得', '地']
    };
    
    if (homophones[correct] && 
        homophones[correct].includes(userAnswer)) {
      return 0.95;
    }
    
    return 0;
  }

  // 生成错误提示
  static generateErrorHint(errorType, details) {
    const hints = {
      'similar_shape': `注意字形区别，不要写成"${details.similar_chars[0]}"`,
      'stroke_error': `笔画数量有误，正确笔画数：${details.correct_strokes}`,
      'structure_error': '注意字的结构和偏旁位置',
      'phonetic_confusion': `不要与"${details.confused_with}"混淆`,
      'other_error': '仔细观察字的写法'
    };
    
    return hints[errorType] || '多练习，仔细观察';
  }
}

module.exports = ErrorAnalysisService;
```

## 6. 数据接口

### 6.1 错题管理接口

#### 获取错题概况
- **接口**: `GET /api/error-collection/summary`
- **权限**: 需要登录
- **返回**:
```json
{
  "totalErrors": 43,
  "masteredCount": 28,
  "pendingCount": 15,
  "weeklyNew": 3,
  "masteryRate": 65.1
}
```

#### 添加错题
- **接口**: `POST /api/error-collection/add`
- **权限**: 需要登录
- **参数**:
```json
{
  "word": "影",
  "userAnswer": "景",
  "errorType": "similar_shape",
  "confidence": 0.9,
  "practiceId": 123,
  "source": {
    "textbook": "人教版",
    "grade": 3,
    "lesson": "第1课"
  }
}
```

## 7. 测试用例

### 7.1 功能测试

#### 错题收集测试
1. **自动收集测试**
   - 验证错误答案自动收集
   - 验证错误类型自动分析
   - 验证重复错误合并

2. **分类准确性测试**
   - 字形相似识别准确率
   - 笔画错误检测准确率
   - 结构问题识别准确率

#### 掌握度评估测试
1. **状态更新测试**
   - 正确答案状态更新
   - 连续正确次数统计
   - 掌握度等级判定

2. **清理建议测试**
   - 清理条件触发
   - 建议列表生成
   - 清理操作执行

### 7.2 性能测试

#### 数据处理性能
- 错题分析响应时间 < 500ms
- 统计数据计算时间 < 1秒
- 大量错题数据处理能力

#### 存储优化
- 错题数据压缩存储
- 历史记录清理策略
- 数据库查询优化 