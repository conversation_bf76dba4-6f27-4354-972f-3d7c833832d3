const app = getApp();
import createHanziWriterContext from 'hanzi-writer-miniprogram';
Page({
  data: {
    // 错题数据
    pendingErrorsByCourse: [], // 待处理错题（按课程分组）
    historyErrorsByCourse: [], // 历史错题（按课程分组）

    // 标签页
    activeTab: 0, // 0: 待处理，1: 历史记录

    // 选择状态
    selectedWords: [], // 选中的待处理错题
    allSelected: false,

    // 统计信息
    totalPendingErrors: 0,
    totalHistoryErrors: 0,

    // 刷新状态
    refreshing: false,

    // 笔画动画弹窗
    showStrokeModal: false,
    currentCharacter: '',
    currentCharacterIndex: 0,
    currentWord: '',
    writerCtx: null
  },

  onLoad() {
    console.log('错题集页面加载');
    
  },

  onShow() {
    console.log('错题集页面显示');

    // 每次显示页面时重新加载错题数据，确保按当前教材筛选
    this.loadErrorWords();

    // 检查是否有错题复习结果
    this.checkErrorReviewResult();
  },

  // 加载错题数据
  loadErrorWords() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    console.log('=== 错题集数据加载开始 ===');
    console.log('原始错题数据总数:', errorWords.length);
    console.log('原始错题数据详情:', errorWords);

    // 获取当前选择的教材信息
    const currentTextbook = wx.getStorageSync('currentTextbook') || {};
    console.log('当前选择的教材信息:', currentTextbook);

    // 筛选当前教材版本的错题
    const filteredErrorWords = errorWords.filter(error => {
      // 如果没有选择教材或错题没有课程信息，则显示所有错题
      if (!currentTextbook.publisher || !error.courseInfo) return true;

      // 检查是否匹配当前教材版本、年级和学期
      const matchPublisher = error.courseInfo.publisherId === currentTextbook.publisher.id;
      const matchGrade = error.courseInfo.gradeId === currentTextbook.grade.id;
      const matchTerm = error.courseInfo.term === currentTextbook.term;

      return matchPublisher && matchGrade && matchTerm;
    });

    console.log('筛选后的错题数据总数:', filteredErrorWords.length);

    // 分析每个错题的状态
    const correctedErrors = [];
    const uncorrectedErrors = [];

    filteredErrorWords.forEach((error, index) => {
      console.log(`\n错题 ${index + 1}:`);
      console.log(`  字词: ${error.word}`);
      console.log(`  课程: ${error.courseInfo?.courseName || error.courseInfo?.courseId || '未知课程'}`);
      console.log(`  订正状态: ${error.corrected ? '已订正' : '未订正'}`);
      console.log(`  订正时间: ${error.correctedTime || '无'}`);

      if (error.corrected === true) {
        correctedErrors.push(error);
      } else {
        uncorrectedErrors.push(error);
      }
    });

    console.log(`\n状态统计:`);
    console.log(`  已订正: ${correctedErrors.length} 个`);
    console.log(`  未订正: ${uncorrectedErrors.length} 个`);

    // 重新定义分类逻辑
    // 待处理：只显示未订正的错题
    const pendingErrors = filteredErrorWords.filter(error => !error.corrected || error.corrected === false);
    // 历史记录：显示所有错题（包括已订正和未订正）
    const historyErrors = [...filteredErrorWords]; // 显示所有错题

    console.log('\n=== 分类结果 ===');
    console.log('待处理错题（未订正）:', pendingErrors.length, '个');
    console.log('待处理错题详情:', pendingErrors.map(e => `${e.word}(${e.corrected ? '已订正' : '未订正'})`));
    console.log('历史错题（所有）:', historyErrors.length, '个');

    // 按课程分组
    const pendingErrorsByCourse = this.groupErrorsByCourse(pendingErrors);
    const historyErrorsByCourse = this.groupErrorsByCourse(historyErrors);

    console.log('\n=== 按课程分组结果 ===');
    console.log('待处理错题（按课程）:', pendingErrorsByCourse.length, '个课程');
    pendingErrorsByCourse.forEach((course, index) => {
      console.log(`  课程 ${index + 1}: ${course.courseName} - ${course.errorCount} 个错题`);
    });

    console.log('历史错题（按课程）:', historyErrorsByCourse.length, '个课程');
    historyErrorsByCourse.forEach((course, index) => {
      console.log(`  课程 ${index + 1}: ${course.courseName} - ${course.errorCount} 个错题`);
    });

    this.setData({
      pendingErrorsByCourse,
      historyErrorsByCourse,
      totalPendingErrors: pendingErrors.length,
      totalHistoryErrors: historyErrors.length,
      selectedWords: [], // 重置选择
      allSelected: false
    });

    console.log('\n=== 页面数据更新完成 ===');
    console.log('页面状态:');
    console.log(`  totalPendingErrors: ${pendingErrors.length}`);
    console.log(`  totalHistoryErrors: ${historyErrors.length}`);
    console.log(`  pendingErrorsByCourse: ${pendingErrorsByCourse.length} 个课程`);
    console.log('=== 错题集数据加载结束 ===\n');
  },

  // 按课程分组错题
  groupErrorsByCourse(errorWords) {
    const groups = {};

    errorWords.forEach(error => {
      const courseInfo = error.courseInfo;
      let courseKey = 'unknown_course';
      let courseName = '未知课程';

      if (courseInfo) {
        // 优先使用courseId构建key
        if (courseInfo.courseId) {
          courseKey = courseInfo.courseId;
        } else {
          // 备用方案：使用组合ID
          const publisherId = courseInfo.publisherId || 'unknown';
          const gradeId = courseInfo.gradeId || 'unknown';
          const term = courseInfo.term || 'unknown';
          courseKey = `${publisherId}_${gradeId}_${term}`;
        }

        // 构建课程标题 - 优先使用完整信息
        if (courseInfo.courseName) {
          // 如果有完整的课程标题，直接使用
          if (courseInfo.courseTitle) {
            courseName = courseInfo.courseTitle;
          } else {
            // 否则构建标题
            const publisher = courseInfo.publisher || '';
            const grade = courseInfo.grade || '';
            const term = courseInfo.term === 'term1' ? '上册' :
              courseInfo.term === 'term2' ? '下册' :
                courseInfo.term || '';

            // 只有当有具体信息时才构建完整标题
            if (publisher && grade) {
              courseName = `${publisher} ${grade}${term} - ${courseInfo.courseName}`;
            } else if (courseInfo.courseName) {
              // 如果没有出版社和年级信息，只显示课程名称
              courseName = courseInfo.courseName;
            }
          }
        } else {
          // 如果没有课程名称，尝试使用课程ID或显示未知
          courseName = courseInfo.courseId || '未知课程';
        }
      }

      if (!groups[courseKey]) {
        groups[courseKey] = {
          courseKey,
          courseName,
          courseInfo,
          words: [],
          errorCount: 0,
          allSelected: false
        };
      }

      // 确保字词数据完整性并处理拼音格式
      let formattedPinyin = error.pinyin || '';

      console.log(`处理字词 "${error.word}" 的拼音:`, error.pinyin);

      // 处理拼音格式：确保每个字对应一个拼音，用空格分隔
      if (error.word && error.word.length > 0) {
        if (formattedPinyin) {
          // 检查原始拼音格式
          let pinyinParts = formattedPinyin.trim().split(/\s+/); // 使用正则分割多个空格
          const wordLength = error.word.length;

          console.log(`字词长度: ${wordLength}, 拼音部分: ${pinyinParts.length}`, pinyinParts);

          // 如果拼音部分数量与字数不匹配，尝试智能分割
          if (pinyinParts.length !== wordLength) {
            if (pinyinParts.length === 1 && wordLength > 1) {
              // 单个拼音字符串，尝试按拼音规则分割
              const singlePinyin = pinyinParts[0];
              pinyinParts = this.splitPinyinString(singlePinyin, wordLength);
              console.log(`智能分割后的拼音:`, pinyinParts);
            }

            // 如果分割后仍然不匹配，进行长度调整
            if (pinyinParts.length < wordLength) {
              // 补充缺少的拼音部分
              const additionalParts = Array(wordLength - pinyinParts.length).fill('');
              pinyinParts = [...pinyinParts, ...additionalParts];
            } else if (pinyinParts.length > wordLength) {
              // 如果拼音过多，只取前面对应数量的拼音
              pinyinParts = pinyinParts.slice(0, wordLength);
            }
          }

          // 重新组合拼音
          formattedPinyin = pinyinParts.join(' ');
        } else {
          // 如果没有拼音，为每个字符创建空的拼音
          formattedPinyin = Array(error.word.length).fill('').join(' ');
        }
      }

      console.log(`最终拼音格式:`, formattedPinyin);

      const pinyinArray = formattedPinyin ? formattedPinyin.split(' ') : [];
      console.log(`拼音数组:`, pinyinArray);

      const wordData = {
        ...error,
        word: error.word || '未知字词',
        pinyin: formattedPinyin,
        pinyinArray: pinyinArray, // 使用已处理的拼音数组
        selected: false,
        attempts: error.attempts || 1,
        // 格式化时间显示
        lastErrorTime: this.formatTimeAgo(error.lastErrorTime),
        correctedTime: error.correctedTime ? this.formatTimeAgo(error.correctedTime) : null
      };

      groups[courseKey].words.push(wordData);
      groups[courseKey].errorCount++;
    });

    // 按课程分组后的排序处理
    const groupsArray = Object.values(groups);

    // 对每个课程组内的错题进行排序：已订正的排在前面
    groupsArray.forEach(group => {
      group.words.sort((a, b) => {
        // 已订正的排前面，未订正的排后面
        if (a.corrected !== b.corrected) {
          return a.corrected ? -1 : 1;
        }
        // 同样订正状态下，按时间排序（最新的在前）
        const timeA = new Date(a.correctedTime || a.lastErrorTime || a.timestamp || 0);
        const timeB = new Date(b.correctedTime || b.lastErrorTime || b.timestamp || 0);
        return timeB - timeA;
      });
    });

    // 按课程的最新错题时间排序
    return groupsArray.sort((a, b) =>
      new Date(b.words[0]?.timestamp || 0) - new Date(a.words[0]?.timestamp || 0)
    );
  },

  // 格式化时间显示
  formatTimeAgo(timeString) {
    if (!timeString) return '未知时间';

    const now = new Date();
    const time = new Date(timeString);
    const diffMs = now - time;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前`;
    } else {
      return '刚刚';
    }
  },

  // 切换标签页
  onTabChange(e) {
    const activeTab = parseInt(e.currentTarget.dataset.index);

    // 切换到待处理标签页时，清空所有选择状态
    if (activeTab === 0) {
      // 重置待处理错题的选择状态
      const { pendingErrorsByCourse } = this.data;
      pendingErrorsByCourse.forEach(course => {
        course.allSelected = false;
        course.words.forEach(word => {
          word.selected = false;
        });
      });

      this.setData({
        activeTab,
        selectedWords: [],
        allSelected: false,
        pendingErrorsByCourse
      });
    } else {
      // 切换到历史记录标签页时，只需要清空选择状态，不需要操作课程数据
      this.setData({
        activeTab,
        selectedWords: [],
        allSelected: false
      });
    }

    console.log(`切换到标签页 ${activeTab}, 已清空选择状态`);
  },

  // 切换课程全选状态
  onToggleCourseSelect(e) {
    const { courseIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex < 0 || courseIndex >= pendingErrorsByCourse.length) return;

    const course = pendingErrorsByCourse[courseIndex];
    const newSelectState = !course.allSelected;

    // 更新课程选择状态
    course.allSelected = newSelectState;
    course.words.forEach(word => {
      word.selected = newSelectState;
    });

    // 更新选中列表
    this.updateSelectedWords();

    this.setData({ pendingErrorsByCourse });
  },

  // 切换单个字词选择状态
  onToggleWordSelect(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex < 0 || courseIndex >= pendingErrorsByCourse.length) return;

    const course = pendingErrorsByCourse[courseIndex];
    if (wordIndex < 0 || wordIndex >= course.words.length) return;

    // 切换字词选择状态
    course.words[wordIndex].selected = !course.words[wordIndex].selected;

    // 更新课程全选状态
    const selectedCount = course.words.filter(word => word.selected).length;
    course.allSelected = selectedCount === course.words.length;

    // 更新选中列表
    this.updateSelectedWords();

    this.setData({ pendingErrorsByCourse });
  },

  // 更新选中的字词列表
  updateSelectedWords() {
    const selectedWords = [];

    this.data.pendingErrorsByCourse.forEach(course => {
      course.words.forEach(word => {
        if (word.selected) {
          selectedWords.push(word);
        }
      });
    });

    // 更新全选状态
    const allSelected = this.data.totalPendingErrors > 0 &&
      selectedWords.length === this.data.totalPendingErrors;

    this.setData({
      selectedWords,
      allSelected
    });
  },

  // 全选/取消全选
  onToggleSelectAll() {
    const { pendingErrorsByCourse, allSelected } = this.data;
    const newSelectState = !allSelected;

    pendingErrorsByCourse.forEach(course => {
      course.allSelected = newSelectState;
      course.words.forEach(word => {
        word.selected = newSelectState;
      });
    });

    this.updateSelectedWords();
    this.setData({ pendingErrorsByCourse });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡，防止按钮点击时触发字词选择
  },

  // 开始错题挑战
  onStartErrorChallenge() {
    const { selectedWords } = this.data;

    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要练习的错题',
        icon: 'none'
      });
      return;
    }

    // 获取主要课程信息（选择最多错题的课程作为主课程）
    const courseGroups = {};
    selectedWords.forEach(error => {
      const courseId = error.courseInfo?.courseId || 'unknown';
      if (!courseGroups[courseId]) {
        courseGroups[courseId] = {
          courseInfo: error.courseInfo,
          count: 0
        };
      }
      courseGroups[courseId].count++;
    });

    // 找到错题最多的课程作为主课程
    const mainCourse = Object.values(courseGroups).reduce((max, current) =>
      current.count > max.count ? current : max
    );

    console.log('错题挑战 - 主课程信息:', mainCourse.courseInfo);
    console.log('错题挑战 - 选择的错题课程分布:', courseGroups);

    // 构建错题挑战数据，保留原有错题的课程信息
    const challengeWords = selectedWords.map(error => ({
      id: error.id,
      word: error.word,
      pinyin: error.pinyin,
      table: error.table || 'errorReview',
      originalCourseInfo: error.courseInfo // 保留原始课程信息用于订正时查找
    }));

    // 创建错题挑战会话 - 使用原有课程信息而不是创建新课程
    const challenge = {
      // 保持原有课程的核心信息
      courseId: mainCourse.courseInfo?.courseId || 'error_review',
      courseName: mainCourse.courseInfo?.courseName || '错题复习',
      courseTitle: mainCourse.courseInfo?.courseTitle || `错题练习 (${selectedWords.length}个字词)`,
      publisher: mainCourse.courseInfo?.publisher || '错题集',
      grade: mainCourse.courseInfo?.grade || '复习',
      term: mainCourse.courseInfo?.term || 'error',
      publisherId: mainCourse.courseInfo?.publisherId || 'error_review',
      gradeId: mainCourse.courseInfo?.gradeId || 'review',

      // 错题复习特有的信息
      selectedWords: challengeWords,
      timestamp: new Date().toISOString(),
      isErrorReview: true, // 标记为错题复习

      // 保存所有涉及的课程信息，用于多课程错题的处理
      involvedCourses: Object.keys(courseGroups).map(courseId => ({
        courseId,
        courseInfo: courseGroups[courseId].courseInfo,
        errorCount: courseGroups[courseId].count
      }))
    };

    console.log('错题挑战会话已创建:', challenge);

    // 保存挑战会话到全局数据
    app.globalData.currentChallenge = challenge;

    wx.showLoading({
      title: '准备错题挑战...'
    });

    // 跳转到练习页面
    setTimeout(() => {
      wx.hideLoading();

      wx.redirectTo({
        url: '/pages/practice/practice',
        success: () => {
          console.log('错题挑战开始，共选择', selectedWords.length, '个错题');
          console.log('涉及课程数量:', Object.keys(courseGroups).length);
        }
      });
    }, 1000);
  },

  // 标记错题为已订正
  onMarkAsCorreected(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < pendingErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < pendingErrorsByCourse[courseIndex].words.length) {

      const word = pendingErrorsByCourse[courseIndex].words[wordIndex];

      wx.showModal({
        title: '确认订正',
        content: `确定将"${word.word}"标记为已订正吗？`,
        success: (res) => {
          if (res.confirm) {
            this.markWordAsCorrected(word.id);
          }
        }
      });
    }
  },

  // 标记字词为已订正
  markWordAsCorrected(wordId) {
    const errorWords = wx.getStorageSync('errorWords') || [];
    const wordIndex = errorWords.findIndex(error => error.id === wordId);

    if (wordIndex >= 0) {
      errorWords[wordIndex].corrected = true;
      errorWords[wordIndex].correctedTime = new Date().toISOString();

      wx.setStorageSync('errorWords', errorWords);

      wx.showToast({
        title: '已标记为订正',
        icon: 'success'
      });

      // 重新加载数据
      this.loadErrorWords();
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });

    setTimeout(() => {
      this.loadErrorWords();
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 页面分享
  onShareAppMessage() {
    const { totalPendingErrors } = this.data;
    return {
      title: `我的错题本有${totalPendingErrors}个待处理错题，一起来练习吧！`,
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-wrongbook.png'
    };
  },

  // 去练习
  onGoToPractice() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 调试错题数据
  debugErrorWords() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    console.log('调试：当前错题数据', errorWords);

    // 检查是否有问题数据
    const problemWords = errorWords.filter(error =>
      error.word === '暂无答案' || error.word === '未知字词' ||
      error.pinyin === '暂无拼音' || !error.word ||
      error.word === '无字词' || error.pinyin === '无拼音'
    );

    if (problemWords.length > 0) {
      console.log('发现问题数据:', problemWords);
      wx.showModal({
        title: '数据问题',
        content: `发现${problemWords.length}条错误数据，是否清除这些无效数据？`,
        success: (res) => {
          if (res.confirm) {
            this.cleanErrorData();
          }
        }
      });
    }
  },

  // 清理错误数据
  cleanErrorData() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    const validWords = errorWords.filter(error =>
      error.word &&
      error.word !== '暂无答案' &&
      error.word !== '未知字词' &&
      error.word !== '无字词' &&
      error.pinyin !== '暂无拼音' &&
      error.pinyin !== '无拼音'
    );

    wx.setStorageSync('errorWords', validWords);
    wx.showToast({
      title: `已清除${errorWords.length - validWords.length}条无效数据`,
      icon: 'success',
      duration: 2000
    });

    // 重新加载数据
    this.loadErrorWords();
  },

  // 手动刷新数据
  onManualRefresh() {
    console.log('用户手动刷新错题集数据');
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    // 延迟一下再刷新，确保loading显示
    setTimeout(() => {
      this.loadErrorWords();
      wx.hideLoading();

      wx.showToast({
        title: '数据已刷新',
        icon: 'success',
        duration: 1000
      });
    }, 500);
  },

  // 长按头部统计区域的调试功能
  onLongPressHeader() {
    wx.showActionSheet({
      itemList: ['清除所有错题', '重新加载数据', '查看错题详情', '查看存储数据'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.clearAllErrors();
        } else if (res.tapIndex === 1) {
          this.onManualRefresh();
        } else if (res.tapIndex === 2) {
          this.showErrorDetails();
        } else if (res.tapIndex === 3) {
          this.showStorageData();
        }
      }
    });
  },

  // 显示错题详情
  showErrorDetails() {
    const errorWords = wx.getStorageSync('errorWords') || [];

    let details = `错题总数: ${errorWords.length}\n\n`;

    errorWords.forEach((error, index) => {
      details += `${index + 1}. ${error.word}\n`;
      details += `   课程: ${error.courseInfo?.courseName || '未知'}\n`;
      details += `   状态: ${error.corrected ? '已订正' : '未订正'}\n`;
      if (error.correctedTime) {
        details += `   订正时间: ${new Date(error.correctedTime).toLocaleString()}\n`;
      }
      details += '\n';
    });

    wx.showModal({
      title: '错题详情',
      content: details,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 显示存储数据
  showStorageData() {
    const errorWords = wx.getStorageSync('errorWords') || [];
    const cachedResults = wx.getStorageSync('cachedPracticeResults');

    let content = `错题数据:\n总数: ${errorWords.length}\n`;
    content += `已订正: ${errorWords.filter(e => e.corrected).length}\n`;
    content += `未订正: ${errorWords.filter(e => !e.corrected).length}\n\n`;

    if (cachedResults) {
      content += `缓存结果:\n${JSON.stringify(cachedResults, null, 2)}`;
    } else {
      content += '无缓存结果';
    }

    console.log('存储数据详情:', { errorWords, cachedResults });

    wx.showModal({
      title: '存储数据',
      content: content,
      showCancel: false,
      confirmText: '确定'
    });
  },

  // 清除所有错题数据
  clearAllErrors() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有错题数据吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          wx.setStorageSync('errorWords', []);
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          });
          // 重新加载数据
          this.loadErrorWords();
        }
      }
    });
  },

  // 长按字词项显示操作菜单
  onLongPressWord(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { pendingErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < pendingErrorsByCourse.length &&
      wordIndex >= 0 && wordIndex < pendingErrorsByCourse[courseIndex].words.length) {

      const word = pendingErrorsByCourse[courseIndex].words[wordIndex];

      wx.showActionSheet({
        itemList: ['标记为已订正', '删除错题'],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 标记为已订正
            this.markWordAsCorrected(word.id);
          } else if (res.tapIndex === 1) {
            // 删除错题
            this.deleteErrorWord(word.id);
          }
        }
      });
    }
  },

  // 删除错题
  deleteErrorWord(wordId) {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个错题吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          const errorWords = wx.getStorageSync('errorWords') || [];
          const filteredWords = errorWords.filter(error => error.id !== wordId);

          wx.setStorageSync('errorWords', filteredWords);

          wx.showToast({
            title: '已删除',
            icon: 'success'
          });

          // 重新加载数据
          this.loadErrorWords();
        }
      }
    });
  },

  // 检查是否有错题复习结果
  checkErrorReviewResult() {
    const cachedResults = wx.getStorageSync('cachedPracticeResults');
    if (cachedResults && cachedResults.isErrorReview) {
      console.log('检测到错题复习完成，缓存结果:', cachedResults);

      // 清除缓存的练习结果
      wx.removeStorageSync('cachedPracticeResults');

      let message = '错题状态已更新';
      if (cachedResults.detailedLog) {
        message = cachedResults.detailedLog;
      } else if (cachedResults.correctedCount > 0) {
        message = `已订正 ${cachedResults.correctedCount} 个错题`;
      }

      wx.showToast({
        title: message,
        icon: 'success',
        duration: 2000
      });
    } else {
      console.log('没有检测到错题复习缓存结果');
    }
  },

  // 智能分割拼音字符串
  splitPinyinString(pinyinString, wordLength) {
    // 如果已经有空格分隔，直接分割
    if (pinyinString.includes(' ')) {
      const parts = pinyinString.trim().split(/\s+/);
      if (parts.length === wordLength) {
        return parts;
      }
    }

    // 对于连续的拼音字符串，尝试智能分割
    if (wordLength === 1) {
      return [pinyinString];
    }

    // 简单的拼音分割规则：按声母韵母分割
    // 这是一个简化版本，实际可能需要更复杂的拼音库
    const pinyinRegex = /[bpmfdtnlgkhjqxzcsrwy]*[aeiouv]+[ng]?[1-4]?/gi;
    const matches = pinyinString.match(pinyinRegex) || [];

    if (matches.length === wordLength) {
      return matches;
    }

    // 如果无法智能分割，尝试平均分割
    if (wordLength > 1) {
      const avgLength = Math.floor(pinyinString.length / wordLength);
      const parts = [];
      for (let i = 0; i < wordLength; i++) {
        const start = i * avgLength;
        const end = i === wordLength - 1 ? pinyinString.length : (i + 1) * avgLength;
        parts.push(pinyinString.substring(start, end));
      }
      return parts;
    }

    // 默认情况：第一个字符显示完整拼音，其他为空
    const result = Array(wordLength).fill('');
    result[0] = pinyinString;
    return result;
  },

  // 点击历史记录字词显示笔画动画
  onShowStrokeAnimation(e) {
    const { courseIndex, wordIndex } = e.currentTarget.dataset;
    const { historyErrorsByCourse } = this.data;

    if (courseIndex >= 0 && courseIndex < historyErrorsByCourse.length &&
        wordIndex >= 0 && wordIndex < historyErrorsByCourse[courseIndex].words.length) {

      const word = historyErrorsByCourse[courseIndex].words[wordIndex];
      console.log('点击字词显示笔画动画:', word.word);

      // 设置当前字词信息
      this.setData({
        showStrokeModal: true,
        currentWord: word.word,
        currentCharacter: word.word.charAt(0), // 默认显示第一个字符
        currentCharacterIndex: 0
      });

      // 使用wx.nextTick确保DOM完全渲染后再初始化
      wx.nextTick(() => {
        setTimeout(() => {
          this.initHanziWriter();
        }, 500);
      });
    }
  },

  // 初始化汉字书写器
  initHanziWriter() {
    const { currentCharacter, showStrokeModal } = this.data;

    if (!currentCharacter || !showStrokeModal) {
      console.warn('没有要显示的字符或弹窗已关闭');
      return;
    }

    // 防止重复初始化
    if (this._isInitializing) {
      console.log('正在初始化中，跳过重复调用');
      return;
    }

    this._isInitializing = true;

    try {
      console.log('开始初始化汉字书写器，字符:', currentCharacter);

      // 清理之前的实例
      if (this.data.writerCtx) {
        this.setData({ writerCtx: null });
      }

      // 检查组件是否存在
      const component = this.selectComponent('#hz-writer');
      if (!component) {
        throw new Error('找不到hanzi-writer-view组件');
      }

      // 创建汉字书写器实例
      const writerCtx = createHanziWriterContext({
        id: 'hz-writer',
        character: currentCharacter,
        page: this,
        onLoadCharDataSuccess: () => {
          console.log('字符数据加载成功:', currentCharacter);
          // 数据加载成功后开始动画
          setTimeout(() => {
            if (this.data.writerCtx && this.data.showStrokeModal) {
              try {
                this.data.writerCtx.loopCharacterAnimation();
              } catch (e) {
                console.error('动画播放失败:', e);
              }
            }
          }, 200);
        },
        onLoadCharDataError: (error) => {
          console.error('字符数据加载失败:', error);
          wx.showToast({
            title: '字符数据加载失败',
            icon: 'none'
          });
        }
      });

      this.setData({ writerCtx });
      console.log('汉字书写器初始化成功:', currentCharacter);

    } catch (error) {
      console.error('汉字书写器初始化失败:', error);
      wx.showToast({
        title: '笔画动画加载失败',
        icon: 'none',
        duration: 2000
      });
    } finally {
      this._isInitializing = false;
    }
  },

  // 切换字符显示
  onSwitchCharacter(e) {
    const { index } = e.currentTarget.dataset;
    const { currentWord } = this.data;

    if (index >= 0 && index < currentWord.length) {
      const newCharacter = currentWord.charAt(index);

      this.setData({
        currentCharacter: newCharacter,
        currentCharacterIndex: index
      });

      // 延迟重新初始化汉字书写器，避免递归调用
      setTimeout(() => {
        this.initHanziWriter();
      }, 100);
    }
  },

  // 关闭笔画动画弹窗
  onCloseStrokeModal() {
    // 清理汉字书写器实例
    if (this.data.writerCtx) {
      try {
        if (this.data.writerCtx.destroy) {
          this.data.writerCtx.destroy();
        }
      } catch (e) {
        console.warn('清理汉字书写器实例失败:', e);
      }
    }

    // 重置初始化标志
    this._isInitializing = false;

    this.setData({
      showStrokeModal: false,
      currentCharacter: '',
      currentCharacterIndex: 0,
      currentWord: '',
      writerCtx: null
    });
  },

  // 重播笔画动画
  onReplayAnimation() {
    const { writerCtx } = this.data;
    try {
      if (writerCtx && writerCtx.animateCharacter) {
        writerCtx.animateCharacter();
      } else if (writerCtx && writerCtx.loopCharacterAnimation) {
        writerCtx.loopCharacterAnimation();
      } else {
        // 如果没有可用的动画方法，重新初始化
        this.initHanziWriter();
      }
    } catch (error) {
      console.error('重播动画失败:', error);
      // 重新初始化汉字书写器
      this.initHanziWriter();
    }
  }
});