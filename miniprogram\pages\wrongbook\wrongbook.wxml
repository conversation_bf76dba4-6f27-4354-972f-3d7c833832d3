<!--错题集页面-->
<view class="wrongbook-container">
  <!-- 标签页导航 -->
  <view class="tabs-nav">
    <view 
      class="tab-item {{activeTab === 0 ? 'active' : ''}}"
      bind:tap="onTabChange"
      data-index="0"
    >
      待处理({{totalPendingErrors}})
    </view>
    <view 
      class="tab-item {{activeTab === 1 ? 'active' : ''}}"
      bind:tap="onTabChange"
      data-index="1"
    >
      历史记录({{totalHistoryErrors}})
    </view>
  </view>

  <!-- 标签页内容 -->
  <view class="tab-content">
    <!-- 待处理错题内容 -->
    <view class="tab-pane {{activeTab === 0 ? 'active' : ''}}" wx:if="{{activeTab === 0}}">
      <!-- 操作栏 -->
      <view class="action-bar" wx:if="{{totalPendingErrors > 0}}">
        <view class="checkbox-wrapper" bind:tap="onToggleSelectAll">
          <view class="checkbox {{allSelected ? 'checked' : ''}}">
            <text class="checkbox-icon" wx:if="{{allSelected}}">✓</text>
          </view>
          <text class="checkbox-label">全选</text>
        </view>
        <view class="selected-info">
          已选择 {{selectedWords.length}} 个错题
        </view>
        <view 
          class="btn-primary {{selectedWords.length === 0 ? 'disabled' : ''}}"
          bind:tap="onStartErrorChallenge"
        >
          开始练习
        </view>
      </view>

      <!-- 错题列表 -->
      <view class="error-list" wx:if="{{totalPendingErrors > 0}}">
        <view 
          class="course-group" 
          wx:for="{{pendingErrorsByCourse}}" 
          wx:key="courseKey"
          wx:for-index="courseIndex"
        >
          <!-- 课程头部 -->
          <view class="course-header">
            <view 
              class="checkbox-wrapper" 
              bind:tap="onToggleCourseSelect"
              data-course-index="{{courseIndex}}"
            >
              <view class="checkbox {{item.allSelected ? 'checked' : ''}}">
                <text class="checkbox-icon" wx:if="{{item.allSelected}}">✓</text>
              </view>
              <view class="course-info">
                <text class="course-name">{{item.courseName}}</text>
                <text class="course-count">{{item.errorCount}}个错题</text>
              </view>
            </view>
          </view>

          <!-- 字词网格 -->
          <view class="words-grid">
            <view 
              class="word-grid-item {{word.selected ? 'selected' : ''}}"
              wx:for="{{item.words}}" 
              wx:key="id"
              wx:for-index="wordIndex"
              wx:for-item="word"
              bind:tap="onToggleWordSelect"
              bind:longpress="onLongPressWord"
              data-course-index="{{courseIndex}}"
              data-word-index="{{wordIndex}}"
            >
              <!-- 田字格背景 -->
              <view class="word-background">
                <!-- 将字词拆分为单个字符，每个字符一个田字格 -->
                <view class="word-grid-container">
                  <view class="tianzige" wx:for="{{word.word}}" wx:key="index" wx:for-item="char">
                    <view class="tianzige-inner">{{char}}</view>
                    <!-- 拼音显示：使用预处理的拼音数组，显示对应位置的拼音 -->
                    <view class="character-pinyin" wx:if="{{word.pinyinArray && index < word.pinyinArray.length}}">
                      {{word.pinyinArray[index] || ''}}
                    </view>
                  </view>
                </view>
              </view>
              
              <!-- 选中标记 -->
              <view class="word-check" wx:if="{{word.selected}}">✓</view>
              
              <!-- 错误信息 -->
              <view class="word-meta">
                <text class="error-count">错{{word.attempts}}次</text>
                <text class="error-time">{{word.lastErrorTime}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{totalPendingErrors === 0}}">
        <view class="empty-icon">📝</view>
        <text class="empty-text">暂无待处理错题</text>
        <view class="btn-primary" bind:tap="onGoToPractice">
          去练习
        </view>
      </view>
    </view>

    <!-- 历史错题内容 -->
    <view class="tab-pane {{activeTab === 1 ? 'active' : ''}}" wx:if="{{activeTab === 1}}">
      <!-- 历史错题列表 -->
      <view class="error-list" wx:if="{{totalHistoryErrors > 0}}">
        <view 
          class="course-group" 
          wx:for="{{historyErrorsByCourse}}" 
          wx:key="courseKey"
          wx:for-index="courseIndex"
        >
          <!-- 课程头部 -->
          <view class="course-header history">
            <view class="course-info">
              <text class="course-name">{{item.courseName}}</text>
              <text class="course-count">{{item.errorCount}}个历史错题</text>
            </view>
          </view>

          <!-- 字词网格 - 历史记录 -->
          <view class="words-grid history">
            <view
              class="word-grid-item history"
              wx:for="{{item.words}}"
              wx:key="id"
              wx:for-index="wordIndex"
              wx:for-item="word"
              bind:tap="onShowStrokeAnimation"
              data-course-index="{{courseIndex}}"
              data-word-index="{{wordIndex}}"
            >
              <!-- 田字格背景 -->
              <view class="word-background">
                <!-- 将字词拆分为单个字符，每个字符一个田字格 -->
                <view class="word-grid-container">
                  <view class="tianzige" wx:for="{{word.word}}" wx:key="index" wx:for-item="char">
                    <view class="tianzige-inner">{{char}}</view>
                    <!-- 拼音显示：使用预处理的拼音数组，显示对应位置的拼音 -->
                    <view class="character-pinyin" wx:if="{{word.pinyinArray && index < word.pinyinArray.length}}">
                      {{word.pinyinArray[index] || ''}}
                    </view>
                  </view>
                </view>
              </view>
              
              <!-- 已订正标记（只有已订正的才显示） -->
              <view class="word-corrected" wx:if="{{word.corrected}}">✓</view>
              
              <!-- 未订正标记（未订正的显示待处理标识） -->
              <view class="word-pending" wx:elif="{{!word.corrected}}">!</view>
              
              <!-- 错误信息 -->
              <view class="word-meta">
                <text class="error-count">错{{word.attempts}}次</text>
                <!-- 显示订正时间或最后错误时间 -->
                <text class="corrected-time" wx:if="{{word.corrected && word.correctedTime}}">
                  {{word.correctedTime}}订正
                </text>
                <text class="error-time" wx:elif="{{word.lastErrorTime}}">
                  {{word.lastErrorTime}}
                </text>
              </view>
              
              <!-- 状态标识 -->
              <view class="word-status">
                <text class="status-tag corrected" wx:if="{{word.corrected}}">已订正</text>
                <text class="status-tag uncorrected" wx:else>未订正</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{totalHistoryErrors === 0}}">
        <view class="empty-icon">🏆</view>
        <text class="empty-text">暂无历史错题记录</text>
      </view>
    </view>
  </view>
</view>

<!-- 笔画动画弹窗 -->
<view class="stroke-modal-mask {{showStrokeModal ? 'show' : ''}}" wx:if="{{showStrokeModal}}" bind:tap="onCloseStrokeModal">
  <view class="stroke-modal-content" catchtap="stopPropagation">
    <!-- 弹窗头部 -->
    <view class="stroke-modal-header">
      <view class="modal-title">笔画演示</view>
      <view class="close-btn" bind:tap="onCloseStrokeModal">✕</view>
    </view>

    <!-- 字符切换器 -->
    <view class="character-switcher" wx:if="{{currentWord.length > 1}}">
      <view class="switcher-label">选择字符：</view>
      <view class="character-tabs">
        <view
          class="character-tab {{currentCharacterIndex === index ? 'active' : ''}}"
          wx:for="{{currentWord}}"
          wx:key="index"
          wx:for-item="char"
          bind:tap="onSwitchCharacter"
          data-index="{{index}}"
        >
          {{char}}
        </view>
      </view>
    </view>

    <!-- 汉字书写器容器 -->
    <view class="hanzi-writer-container">
      <hanzi-writer-view
        id="hz-writer"
        width="300"
        height="300"
        wx:if="{{showStrokeModal}}"
      />
    </view>

    <!-- 当前字符信息 -->
    <view class="character-info">
      <view class="current-character">{{currentCharacter}}</view>
      <view class="character-description">正在演示：{{currentCharacter}} 的笔画顺序</view>
    </view>

    <!-- 操作按钮 -->
    <view class="stroke-modal-actions">
      <view class="action-btn secondary" bind:tap="onReplayAnimation">
        重播动画
      </view>
      <view class="action-btn primary" bind:tap="onCloseStrokeModal">
        关闭
      </view>
    </view>
  </view>
</view>